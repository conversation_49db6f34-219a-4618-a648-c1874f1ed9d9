import React, { Suspense, lazy } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider } from './contexts/AuthContext'
import { ToastProvider } from './contexts/ToastContext'
import { useAuth } from './hooks/useAuth'

// Layout components
import MainLayout from './layouts/MainLayout'
import AuthLayout from './layouts/AuthLayout'

// Common components
import ErrorBoundary from './components/common/ErrorBoundary'
import { PageLoading } from './components/common/LoadingStates'

// Auth pages (keep these as regular imports for faster initial load)
import Login from './pages/auth/Login'

// Lazy load heavy admin components for better performance
const AdminDashboard = lazy(() => import('./pages/admin/Dashboard'))
const LoanManagement = lazy(() => import('./pages/admin/LoanManagement'))
const InvestmentAllocation = lazy(() => import('./pages/admin/InvestmentAllocation'))
const InvestorManagement = lazy(() => import('./pages/admin/InvestorManagement'))
const UserManagement = lazy(() => import('./pages/admin/UserManagement'))
const InvestmentManagement = lazy(() => import('./pages/admin/InvestmentManagement'))
const RepaymentManagement = lazy(() => import('./pages/admin/RepaymentManagement'))

// Lazy load analytics components (typically heavy with data processing)
const PlatformAnalytics = lazy(() => import('./pages/admin/analytics/PlatformAnalytics'))
const UserAnalytics = lazy(() => import('./pages/admin/analytics/UserAnalytics'))
const FinancialAnalytics = lazy(() => import('./pages/admin/analytics/FinancialAnalytics'))
const Reports = lazy(() => import('./pages/admin/analytics/Reports'))
const ApiTesting = lazy(() => import('./pages/admin/tools/ApiTesting'))
const AdminSettings = lazy(() => import('./pages/admin/Settings'))

// Lazy load investor components
const InvestorDashboard = lazy(() => import('./pages/investor/Dashboard'))
const Portfolio = lazy(() => import('./pages/investor/Portfolio'))
const Performance = lazy(() => import('./pages/investor/analytics/Performance'))
const RiskAnalysis = lazy(() => import('./pages/investor/analytics/RiskAnalysis'))
const Diversification = lazy(() => import('./pages/investor/analytics/Diversification'))
const Trends = lazy(() => import('./pages/investor/analytics/Trends'))
const InvestorSettings = lazy(() => import('./pages/investor/Settings'))

// Lazy load borrower components
const BorrowerDashboard = lazy(() => import('./pages/borrower/Dashboard'))
const LoanApplication = lazy(() => import('./pages/borrower/LoanApplication'))
const MyLoans = lazy(() => import('./pages/borrower/MyLoans'))
const LoanAnalysis = lazy(() => import('./pages/borrower/analytics/LoanAnalysis'))
const RepaymentAnalysis = lazy(() => import('./pages/borrower/analytics/RepaymentAnalysis'))
const BorrowerReports = lazy(() => import('./pages/borrower/analytics/BorrowerReports'))
const BorrowerSettings = lazy(() => import('./pages/borrower/Settings'))



const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false
        }
        return failureCount < 3
      },
      refetchOnWindowFocus: false, // Disable refetch on window focus for better performance
      refetchOnMount: true,
      refetchOnReconnect: 'always',
    },
    mutations: {
      retry: 1, // Retry mutations once on failure
    },
  },
})

function ProtectedRoute({ children, allowedRoles = [] }) {
  const { user, loading } = useAuth()

  console.log('🛡️ ProtectedRoute - loading:', loading, 'user:', user?.email || 'none', 'allowedRoles:', allowedRoles)

  if (loading) {
    console.log('🔄 ProtectedRoute showing loading state')
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-navy-50 to-cream-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary-200 border-t-primary-600 mx-auto mb-4"></div>
          <p className="text-navy-600 font-medium">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    console.log('🚫 ProtectedRoute - no user, redirecting to login')
    return <Navigate to="/auth/login" replace />
  }

  if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
    console.log('🚫 ProtectedRoute - user role not allowed:', user.role, 'allowed:', allowedRoles)
    return <Navigate to="/unauthorized" replace />
  }

  console.log('✅ ProtectedRoute - access granted for user:', user.email, 'role:', user.role)
  return children
}

function AppRoutes() {
  const { user } = useAuth()

  return (
    <Routes>
      {/* Public routes - redirect to login */}
      <Route path="/" element={<Navigate to="/auth/login" replace />} />

      {/* Auth routes */}
      <Route path="/auth" element={<AuthLayout />}>
        <Route path="login" element={<Login />} />
      </Route>

      {/* Protected routes */}
      <Route path="/app" element={
        <ProtectedRoute>
          <MainLayout />
        </ProtectedRoute>
      }>
        {/* Admin routes with lazy loading */}
        <Route path="admin" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <AdminDashboard />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/loans" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <LoanManagement />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/loans/:loanId/allocate" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <InvestmentAllocation />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/investors" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <InvestorManagement />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/users" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <UserManagement />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/investments" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <InvestmentManagement />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/repayments" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <RepaymentManagement />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/analytics/platform" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <PlatformAnalytics />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/analytics/users" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <UserAnalytics />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/analytics/financial" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <FinancialAnalytics />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/analytics/reports" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <Reports />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/tools/api-testing" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <ApiTesting />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="admin/settings" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Suspense fallback={<PageLoading />}>
              <AdminSettings />
            </Suspense>
          </ProtectedRoute>
        } />

        {/* Investor routes with lazy loading */}
        <Route path="investor" element={
          <ProtectedRoute allowedRoles={['investor']}>
            <Suspense fallback={<PageLoading />}>
              <InvestorDashboard />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="investor/portfolio" element={
          <ProtectedRoute allowedRoles={['investor']}>
            <Suspense fallback={<PageLoading />}>
              <Portfolio />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="investor/analytics/performance" element={
          <ProtectedRoute allowedRoles={['investor']}>
            <Suspense fallback={<PageLoading />}>
              <Performance />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="investor/analytics/risk" element={
          <ProtectedRoute allowedRoles={['investor']}>
            <Suspense fallback={<PageLoading />}>
              <RiskAnalysis />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="investor/analytics/diversification" element={
          <ProtectedRoute allowedRoles={['investor']}>
            <Suspense fallback={<PageLoading />}>
              <Diversification />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="investor/analytics/trends" element={
          <ProtectedRoute allowedRoles={['investor']}>
            <Suspense fallback={<PageLoading />}>
              <Trends />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="investor/settings" element={
          <ProtectedRoute allowedRoles={['investor']}>
            <Suspense fallback={<PageLoading />}>
              <InvestorSettings />
            </Suspense>
          </ProtectedRoute>
        } />

        {/* Borrower routes with lazy loading */}
        <Route path="borrower" element={
          <ProtectedRoute allowedRoles={['borrower']}>
            <Suspense fallback={<PageLoading />}>
              <BorrowerDashboard />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="borrower/apply" element={
          <ProtectedRoute allowedRoles={['borrower']}>
            <Suspense fallback={<PageLoading />}>
              <LoanApplication />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="borrower/loans" element={
          <ProtectedRoute allowedRoles={['borrower']}>
            <Suspense fallback={<PageLoading />}>
              <MyLoans />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="borrower/analytics/loans" element={
          <ProtectedRoute allowedRoles={['borrower']}>
            <Suspense fallback={<PageLoading />}>
              <LoanAnalysis />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="borrower/analytics/repayments" element={
          <ProtectedRoute allowedRoles={['borrower']}>
            <Suspense fallback={<PageLoading />}>
              <RepaymentAnalysis />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="borrower/analytics/reports" element={
          <ProtectedRoute allowedRoles={['borrower']}>
            <Suspense fallback={<PageLoading />}>
              <BorrowerReports />
            </Suspense>
          </ProtectedRoute>
        } />
        <Route path="borrower/settings" element={
          <ProtectedRoute allowedRoles={['borrower']}>
            <Suspense fallback={<PageLoading />}>
              <BorrowerSettings />
            </Suspense>
          </ProtectedRoute>
        } />

        {/* Default redirect based on user role */}
        <Route index element={
          user?.role === 'admin' ? <Navigate to="/app/admin" replace /> :
          user?.role === 'investor' ? <Navigate to="/app/investor" replace /> :
          user?.role === 'borrower' ? <Navigate to="/app/borrower" replace /> :
          <Navigate to="/" replace />
        } />
      </Route>

      {/* Catch all */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  )
}

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <AuthProvider>
            <Router>
              <div className="min-h-screen bg-secondary-50">
                <AppRoutes />
              </div>
            </Router>
          </AuthProvider>
        </ToastProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  )
}

export default App
