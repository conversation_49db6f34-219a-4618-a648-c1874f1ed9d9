import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../hooks/useAuth'
import { Eye, EyeOff, AlertCircle } from 'lucide-react'

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  })
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const { signIn, user, loading: authLoading } = useAuth()
  const navigate = useNavigate()

  // Redirect user to appropriate dashboard after successful login
  useEffect(() => {
    if (user && !authLoading) {
      console.log('🎯 User loaded, redirecting to dashboard...')
      if (user.role === 'admin') {
        navigate('/app/admin', { replace: true })
      } else if (user.role === 'investor') {
        navigate('/app/investor', { replace: true })
      } else if (user.role === 'borrower') {
        navigate('/app/borrower', { replace: true })
      } else {
        navigate('/app', { replace: true })
      }
    }
  }, [user, authLoading, navigate])

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
    setError('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    console.log('🔐 Starting login process...')
    const { data, error } = await signIn(formData.email, formData.password)

    if (error) {
      console.error('❌ Login error:', error.message)
      setError(error.message)
      setLoading(false)
    } else {
      console.log('✅ Login successful:', data)
      console.log('🔄 Auth context will handle navigation after profile is loaded...')
      // Don't navigate immediately - let the auth context and ProtectedRoute handle it
      // The loading state will be managed by the auth context
    }
  }

  return (
    <div>
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-navy-900 mb-2">Welcome back</h2>
        <p className="text-secondary-600">
          Sign in to access your dashboard
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-danger-50/80 backdrop-blur-sm border border-danger-200 rounded-xl p-4 flex items-center">
            <AlertCircle className="h-5 w-5 text-danger-600 mr-3" />
            <span className="text-danger-700 text-sm font-medium">{error}</span>
          </div>
        )}

        <div>
          <label htmlFor="email" className="label">
            Email address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={formData.email}
            onChange={handleChange}
            className="input-field"
            placeholder="Enter your email"
          />
        </div>

        <div>
          <label htmlFor="password" className="label">
            Password
          </label>
          <div className="relative">
            <input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="current-password"
              required
              value={formData.password}
              onChange={handleChange}
              className="input-field pr-10"
              placeholder="Enter your password"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-secondary-400" />
              ) : (
                <Eye className="h-5 w-5 text-secondary-400" />
              )}
            </button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              id="remember-me"
              name="remember-me"
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
            />
            <label htmlFor="remember-me" className="ml-2 block text-sm text-secondary-700">
              Remember me
            </label>
          </div>

          <div className="text-sm">
            <a href="#" className="text-primary-600 hover:text-primary-500 font-medium">
              Forgot your password?
            </a>
          </div>
        </div>

        <div>
          <button
            type="submit"
            disabled={loading || authLoading}
            className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-base"
          >
            {(loading || authLoading) ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Signing in...</span>
              </div>
            ) : (
              'Sign in'
            )}
          </button>
        </div>
      </form>
    </div>
  )
}

export default Login
