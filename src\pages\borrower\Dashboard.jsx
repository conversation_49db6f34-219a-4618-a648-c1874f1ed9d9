import React from 'react'
import { FileText, DollarSign, Calendar } from 'lucide-react'
import { useAuth } from '../../hooks/useAuth'
import { useLoans, useRepayments } from '../../hooks/useApi'

const BorrowerDashboard = () => {
  const { user } = useAuth()
  const { data: loans, isLoading: loansLoading } = useLoans({ borrower_id: user?.id })
  const { data: _repayments, isLoading: repaymentsLoading } = useRepayments({ borrower_id: user?.id })

  // Calculate stats from real data
  const stats = {
    totalLoans: loans?.length || 0,
    totalBorrowed: loans?.reduce((sum, loan) => sum + (loan.amount || 0), 0) || 0,
    outstandingAmount: loans?.reduce((sum, loan) => sum + (loan.remaining_balance || 0), 0) || 0,
    nextPaymentDue: loans?.find(loan => loan.status === 'active')?.next_payment_date || null,
  }

  const isLoading = loansLoading || repaymentsLoading

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Borrower Dashboard</h1>
        <p className="text-secondary-600 mt-2">
          Loan overview and repayment tracking
        </p>
      </div>

      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="bg-blue-100 p-3 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Total Loans</p>
                <p className="text-2xl font-bold text-secondary-900">{stats.totalLoans}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="bg-green-100 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Total Borrowed</p>
                <p className="text-2xl font-bold text-secondary-900">
                  ${stats.totalBorrowed.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="bg-orange-100 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Outstanding</p>
                <p className="text-2xl font-bold text-orange-900">
                  ${stats.outstandingAmount.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="bg-purple-100 p-3 rounded-lg">
                <Calendar className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Next Payment</p>
                <p className="text-lg font-bold text-secondary-900">
                  {stats.nextPaymentDue ? new Date(stats.nextPaymentDue).toLocaleDateString() : 'No payments due'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Summary */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4">Loan Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Repayment Progress</p>
              <p className="text-2xl font-bold text-green-600">
                {(((stats.totalBorrowed - stats.outstandingAmount) / stats.totalBorrowed) * 100).toFixed(1)}%
              </p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Amount Repaid</p>
              <p className="text-2xl font-bold text-gray-900">
                ${(stats.totalBorrowed - stats.outstandingAmount).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BorrowerDashboard
