import React, { useState } from 'react'
import { AlertTriangle, Shield, TrendingDown, BarChart3 } from 'lucide-react'
import { useAuth } from '../../../hooks/useAuth'

const RiskAnalysisPage = () => {
  const [timeframe, setTimeframe] = useState('30d')
  const { user: _user } = useAuth()

  // Mock data - will be replaced with real data from Supabase
  const riskStats = {
    riskScore: 3.2,
    volatility: 12.5,
    maxDrawdown: 8.3,
    sharpeRatio: 1.45
  }

  const getRiskLevel = (score) => {
    if (score <= 2) return { level: 'Low', color: 'green' }
    if (score <= 4) return { level: 'Medium', color: 'yellow' }
    return { level: 'High', color: 'red' }
  }

  const riskLevel = getRiskLevel(riskStats.riskScore)

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Risk Analysis</h1>
        <p className="text-secondary-600 mt-2">
          Analyze your portfolio risk exposure and risk-adjusted returns
        </p>
      </div>

      {/* Timeframe Selector */}
      <div className="mb-6">
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
          <option value="1y">Last year</option>
        </select>
      </div>

      {/* Risk Overview */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Overview</h3>
        <div className="flex items-center mb-4">
          <div className={`bg-${riskLevel.color}-100 p-3 rounded-lg mr-4`}>
            <AlertTriangle className={`h-8 w-8 text-${riskLevel.color}-600`} />
          </div>
          <div>
            <p className="text-2xl font-bold text-secondary-900">{riskLevel.level} Risk</p>
            <p className="text-sm text-secondary-600">Risk Score: {riskStats.riskScore}/5</p>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`bg-${riskLevel.color}-600 h-2 rounded-full`}
            style={{ width: `${(riskStats.riskScore / 5) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Risk Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-orange-100 p-3 rounded-lg">
              <BarChart3 className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Volatility</p>
              <p className="text-2xl font-bold text-secondary-900">{riskStats.volatility}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-red-100 p-3 rounded-lg">
              <TrendingDown className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Max Drawdown</p>
              <p className="text-2xl font-bold text-secondary-900">{riskStats.maxDrawdown}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Sharpe Ratio</p>
              <p className="text-2xl font-bold text-secondary-900">{riskStats.sharpeRatio}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Risk Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Factors</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Credit Risk</span>
              <div className="flex items-center">
                <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                  <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '60%' }}></div>
                </div>
                <span className="text-sm font-medium">Medium</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Market Risk</span>
              <div className="flex items-center">
                <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: '30%' }}></div>
                </div>
                <span className="text-sm font-medium">Low</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Liquidity Risk</span>
              <div className="flex items-center">
                <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                  <div className="bg-red-600 h-2 rounded-full" style={{ width: '80%' }}></div>
                </div>
                <span className="text-sm font-medium">High</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Recommendations</h3>
          <div className="space-y-3">
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm font-medium text-blue-900">Diversification</p>
              <p className="text-xs text-blue-700">Consider spreading investments across more loan categories</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <p className="text-sm font-medium text-green-900">Risk Management</p>
              <p className="text-xs text-green-700">Your current risk level is within acceptable limits</p>
            </div>
            <div className="p-3 bg-yellow-50 rounded-lg">
              <p className="text-sm font-medium text-yellow-900">Monitoring</p>
              <p className="text-xs text-yellow-700">Keep monitoring market conditions for potential adjustments</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RiskAnalysisPage
