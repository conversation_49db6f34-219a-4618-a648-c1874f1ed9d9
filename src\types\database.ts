/**
 * Generated Supabase Database Types
 * Auto-generated from Supabase schema - DO NOT EDIT MANUALLY
 * 
 * To regenerate: npx supabase gen types typescript --project-id kajmfipamsvgfhcpxxoq
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.12 (cd3cf9e)"
  }
  public: {
    Tables: {
      audit_logs: {
        Row: {
          action: string
          admin_id: string
          comment: string | null
          created_at: string | null
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          resource_id: string
          resource_type: string
          user_agent: string | null
        }
        Insert: {
          action: string
          admin_id: string
          comment?: string | null
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          resource_id: string
          resource_type: string
          user_agent?: string | null
        }
        Update: {
          action?: string
          admin_id?: string
          comment?: string | null
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          resource_id?: string
          resource_type?: string
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_admin_id_fkey"   
            columns: ["admin_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      investments: {
        Row: {
          actual_return: number | null
          amount: number
          completed_at: string | null
          created_at: string | null
          expected_return: number | null
          id: string
          invested_at: string | null
          investor_id: string
          loan_id: string
          percentage: number
          status: Database["public"]["Enums"]["investment_status"] | null
          updated_at: string | null
        }
        Insert: {
          actual_return?: number | null
          amount: number
          completed_at?: string | null
          created_at?: string | null
          expected_return?: number | null
          id?: string
          invested_at?: string | null
          investor_id: string
          loan_id: string
          percentage: number
          status?: Database["public"]["Enums"]["investment_status"] | null
          updated_at?: string | null
        }
        Update: {
          actual_return?: number | null
          amount?: number
          completed_at?: string | null
          created_at?: string | null
          expected_return?: number | null
          id?: string
          invested_at?: string | null
          investor_id?: string
          loan_id?: string
          percentage?: number
          status?: Database["public"]["Enums"]["investment_status"] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "investments_investor_id_fkey"
            columns: ["investor_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_loan_id_fkey"   
            columns: ["loan_id"]
            isOneToOne: false
            referencedRelation: "loans"
            referencedColumns: ["id"]
          },
        ]
      }
      investor_distributions: {
        Row: {
          amount: number
          created_at: string | null
          distributed_at: string | null
          id: string
          interest_amount: number
          investment_id: string
          investor_id: string
          percentage: number
          principal_amount: number
          repayment_id: string
        }
        Insert: {
          amount: number
          created_at?: string | null
          distributed_at?: string | null
          id?: string
          interest_amount: number
          investment_id: string
          investor_id: string
          percentage: number
          principal_amount: number
          repayment_id: string
        }
        Update: {
          amount?: number
          created_at?: string | null
          distributed_at?: string | null
          id?: string
          interest_amount?: number
          investment_id?: string
          investor_id?: string
          percentage?: number
          principal_amount?: number
          repayment_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "investor_distributions_investment_id_fkey"
            columns: ["investment_id"]
            isOneToOne: false
            referencedRelation: "investments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investor_distributions_investor_id_fkey"
            columns: ["investor_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investor_distributions_repayment_id_fkey"
            columns: ["repayment_id"]
            isOneToOne: false
            referencedRelation: "repayments"
            referencedColumns: ["id"]
          },
        ]
      }
      loans: {
        Row: {
          amount: number
          approved_at: string | null
          borrower_id: string
          created_at: string | null
          description: string | null
          end_date: string | null
          funded_amount: number | null
          funded_at: string | null
          id: string
          interest_rate: number
          monthly_payment: number | null
          next_payment_date: string | null
          purpose: string | null
          remaining_amount: number | null
          start_date: string | null
          status: Database["public"]["Enums"]["loan_status"] | null
          term_months: number
          title: string
          total_repayment: number | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          approved_at?: string | null
          borrower_id: string
          created_at?: string | null
          description?: string | null
          end_date?: string | null
          funded_amount?: number | null
          funded_at?: string | null
          id?: string
          interest_rate: number
          monthly_payment?: number | null
          next_payment_date?: string | null
          purpose?: string | null
          remaining_amount?: number | null
          start_date?: string | null
          status?: Database["public"]["Enums"]["loan_status"] | null
          term_months: number
          title: string
          total_repayment?: number | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          approved_at?: string | null
          borrower_id?: string
          created_at?: string | null
          description?: string | null
          end_date?: string | null
          funded_amount?: number | null
          funded_at?: string | null
          id?: string
          interest_rate?: number
          monthly_payment?: number | null
          next_payment_date?: string | null
          purpose?: string | null
          remaining_amount?: number | null
          start_date?: string | null
          status?: Database["public"]["Enums"]["loan_status"] | null
          term_months?: number
          title?: string
          total_repayment?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "loans_borrower_id_fkey"
            columns: ["borrower_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      repayments: {
        Row: {
          amount: number
          created_at: string | null
          due_date: string
          id: string
          interest_amount: number
          loan_id: string
          paid_date: string | null
          payment_method: string | null
          principal_amount: number
          status: Database["public"]["Enums"]["repayment_status"] | null
          transaction_reference: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          due_date: string
          id?: string
          interest_amount: number
          loan_id: string
          paid_date?: string | null
          payment_method?: string | null
          principal_amount: number
          status?: Database["public"]["Enums"]["repayment_status"] | null
          transaction_reference?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          due_date?: string
          id?: string
          interest_amount?: number
          loan_id?: string
          paid_date?: string | null
          payment_method?: string | null
          principal_amount?: number
          status?: Database["public"]["Enums"]["repayment_status"] | null
          transaction_reference?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "repayments_loan_id_fkey"
            columns: ["loan_id"]
            isOneToOne: false
            referencedRelation: "loans"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          created_at: string | null
          email: string
          email_verified: boolean | null
          first_name: string
          id: string
          is_active: boolean | null
          last_name: string
          phone: string | null
          profile_completed: boolean | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          email_verified?: boolean | null
          first_name: string
          id: string
          is_active?: boolean | null
          last_name: string
          phone?: string | null
          profile_completed?: boolean | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          email_verified?: boolean | null
          first_name?: string
          id?: string
          is_active?: boolean | null
          last_name?: string
          phone?: string | null
          profile_completed?: boolean | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Relationships: []
      }
      wallet_transactions: {
        Row: {
          amount: number
          balance_after: number
          balance_before: number
          created_at: string | null
          description: string | null
          id: string
          processed_at: string | null
          reference_id: string | null
          reference_type: string | null
          status:
            | Database["public"]["Enums"]["wallet_transaction_status"]
            | null
          transaction_date: string | null
          type: Database["public"]["Enums"]["transaction_type"]
          user_id: string
          wallet_id: string
        }
        Insert: {
          amount: number
          balance_after: number
          balance_before: number
          created_at?: string | null
          description?: string | null
          id?: string
          processed_at?: string | null
          reference_id?: string | null
          reference_type?: string | null
          status?:
            | Database["public"]["Enums"]["wallet_transaction_status"]
            | null
          transaction_date?: string | null
          type: Database["public"]["Enums"]["transaction_type"]
          user_id: string
          wallet_id: string
        }
        Update: {
          amount?: number
          balance_after?: number
          balance_before?: number
          created_at?: string | null
          description?: string | null
          id?: string
          processed_at?: string | null
          reference_id?: string | null
          reference_type?: string | null
          status?:
            | Database["public"]["Enums"]["wallet_transaction_status"]
            | null
          transaction_date?: string | null
          type?: Database["public"]["Enums"]["transaction_type"]
          user_id?: string
          wallet_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "wallet_transactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "wallet_transactions_wallet_id_fkey"
            columns: ["wallet_id"]
            isOneToOne: false
            referencedRelation: "wallets"
            referencedColumns: ["id"]
          },
        ]
      }
      wallets: {
        Row: {
          available_balance: number | null
          created_at: string | null
          id: string
          invested_balance: number | null
          total_balance: number | null
          total_deposits: number | null
          total_earnings: number | null
          total_withdrawals: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          available_balance?: number | null
          created_at?: string | null
          id?: string
          invested_balance?: number | null
          total_balance?: number | null
          total_deposits?: number | null
          total_earnings?: number | null
          total_withdrawals?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          available_balance?: number | null
          created_at?: string | null
          id?: string
          invested_balance?: number | null
          total_balance?: number | null
          total_deposits?: number | null
          total_earnings?: number | null
          total_withdrawals?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "wallets_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      atomic_investment_allocation: {
        Args: {
          p_investor_id: string
          p_loan_id: string
          p_amount: number
          p_description?: string
        }
        Returns: Json
      }
      atomic_repayment_distribution: {
        Args: {
          p_loan_id: string
          p_repayment_amount: number
          p_description?: string
        }
        Returns: Json
      }
      atomic_wallet_deposit: {
        Args: { p_user_id: string; p_amount: number; p_description?: string }
        Returns: Json
      }
      atomic_wallet_withdrawal: {
        Args: { p_user_id: string; p_amount: number; p_description?: string }
        Returns: Json
      }
      create_user_profile: {
        Args: {
          user_id: string
          user_email: string
          first_name: string
          last_name: string
          user_role?: Database["public"]["Enums"]["user_role"]
          phone?: string
        }
        Returns: string
      }
      get_max_interest_rate: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_user_role: {
        Args: Record<PropertyKey, never>
        Returns: Database["public"]["Enums"]["user_role"]
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      update_user_profile: {
        Args: {
          user_id: string
          first_name?: string
          last_name?: string
          phone?: string
        }
        Returns: boolean
      }
      validate_interest_rate: {
        Args: { rate: number }
        Returns: boolean
      }
    }
    Enums: {
      investment_status: "pending" | "active" | "completed" | "cancelled"
      loan_status:
        | "draft"
        | "pending"
        | "approved"
        | "funded"
        | "active"
        | "completed"
        | "defaulted"
        | "cancelled"
      repayment_status: "pending" | "completed" | "overdue" | "failed"
      transaction_type:
        | "deposit"
        | "withdrawal"
        | "investment"
        | "repayment"
        | "fee"
        | "interest"
      user_role: "admin" | "investor" | "borrower"
      wallet_transaction_status:
        | "pending"
        | "completed"
        | "failed"
        | "cancelled"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      investment_status: ["pending", "active", "completed", "cancelled"],
      loan_status: [
        "draft",
        "pending",
        "approved",
        "funded",
        "active",
        "completed",
        "defaulted",
        "cancelled",
      ],
      repayment_status: ["pending", "completed", "overdue", "failed"],
      transaction_type: [
        "deposit",
        "withdrawal",
        "investment",
        "repayment",
        "fee",
        "interest",
      ],
      user_role: ["admin", "investor", "borrower"],
      wallet_transaction_status: [
        "pending",
        "completed",
        "failed",
        "cancelled",
      ],
    },
  },
} as const
