import React, { useState } from 'react'
import { DollarSign, TrendingUp, <PERSON><PERSON><PERSON>, Bar<PERSON>hart3 } from 'lucide-react'
import { useLoans, useInvestments, useRepayments } from '../../../hooks/useApi'

const FinancialAnalyticsPage = () => {
  const [timeframe, setTimeframe] = useState('30d')
  const { data: loans, isLoading: loansLoading } = useLoans()
  const { data: investments, isLoading: investmentsLoading } = useInvestments()
  const { data: repayments, isLoading: repaymentsLoading } = useRepayments()

  const isLoading = loansLoading || investmentsLoading || repaymentsLoading

  // Calculate financial stats from real data
  const financialStats = {
    totalVolume: investments?.reduce((sum, inv) => sum + (inv.amount || 0), 0) || 0,
    totalRevenue: repayments?.reduce((sum, payment) => sum + (payment.interest_amount || 0), 0) || 0,
    averageROI: investments?.length ?
      investments.reduce((sum, inv) => sum + (inv.roi_percentage || 0), 0) / investments.length : 0,
    defaultRate: loans?.length ?
      (loans.filter(loan => loan.status === 'defaulted').length / loans.length) * 100 : 0
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Financial Analytics</h1>
        <p className="text-secondary-600 mt-2">
          Financial performance metrics and revenue analytics
        </p>
      </div>

      {/* Timeframe Selector */}
      <div className="mb-6">
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
          <option value="1y">Last year</option>
        </select>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Total Volume</p>
              <p className="text-2xl font-bold text-secondary-900">
                ${financialStats.totalVolume.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Total Revenue</p>
              <p className="text-2xl font-bold text-secondary-900">
                ${financialStats.totalRevenue.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-lg">
              <BarChart3 className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Average ROI</p>
              <p className="text-2xl font-bold text-secondary-900">{financialStats.averageROI}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-red-100 p-3 rounded-lg">
              <PieChart className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Default Rate</p>
              <p className="text-2xl font-bold text-secondary-900">{financialStats.defaultRate}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Placeholder for detailed analytics */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Performance Trends</h3>
        <p className="text-gray-600">Detailed financial analytics charts and insights will be implemented here.</p>
      </div>
    </div>
  )
}

export default FinancialAnalyticsPage
