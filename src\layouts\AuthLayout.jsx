import { Outlet } from 'react-router-dom'
import Logo from '../components/common/Logo'
import { TrendingUp, Shield, Users, Zap } from 'lucide-react'

const AuthLayout = () => {
  const features = [
    {
      icon: TrendingUp,
      title: "Smart Investments",
      description: "AI-powered portfolio optimization for maximum returns"
    },
    {
      icon: Shield,
      title: "Secure Platform",
      description: "Bank-level security with regulatory compliance"
    },
    {
      icon: Users,
      title: "Community Driven",
      description: "Connect with verified investors and borrowers"
    },
    {
      icon: Zap,
      title: "Instant Processing",
      description: "Lightning-fast loan approvals and fund transfers"
    }
  ]

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-cream-50 via-white to-primary-50">
      {/* Left side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-navy-900 via-primary-800 to-primary-600"></div>

        {/* Decorative elements */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-cream-400/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-32 right-16 w-24 h-24 bg-primary-400/30 rounded-full blur-lg"></div>
        <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-cream-300/25 rounded-full blur-md"></div>

        <div className="relative z-10 flex flex-col justify-center items-center text-center px-12 py-16">
          <div className="mb-8">
            <Logo size="xl" variant="icon" className="mb-6" />
            <h1 className="text-5xl font-bold text-white mb-4 tracking-tight">
              Ontare
            </h1>
            <p className="text-xl text-cream-100 mb-12 leading-relaxed">
              The future of peer-to-peer lending
            </p>
          </div>

          <div className="space-y-6 max-w-sm">
            {features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4 text-left">
                <div className="flex-shrink-0 w-10 h-10 bg-cream-200/20 rounded-lg flex items-center justify-center">
                  <feature.icon className="w-5 h-5 text-cream-200" />
                </div>
                <div>
                  <h3 className="text-cream-100 font-semibold text-sm mb-1">
                    {feature.title}
                  </h3>
                  <p className="text-cream-300 text-xs leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right side - Auth forms */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 relative">
        {/* Background decoration for mobile */}
        <div className="lg:hidden absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-primary-600/10 to-transparent"></div>

        <div className="max-w-md w-full space-y-8 relative z-10">
          <div className="lg:hidden text-center mb-8">
            <Logo size="lg" className="justify-center mb-4" />
            <p className="text-secondary-600 text-sm">
              Modern loan management platform
            </p>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-large p-8 border border-cream-200/50">
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  )
}

export default AuthLayout
