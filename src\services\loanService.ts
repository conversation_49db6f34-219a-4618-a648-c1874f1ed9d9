import { supabase } from '../config/supabase'
import { TABLES, LOAN_STATUS } from '../config/supabase'
import type {
  Loan,
  LoanInsert,
  LoanUpdate,
  LoanStatus,
  ApiResponse
} from '../types'

/**
 * Loan Management Service
 * Handles all loan-related API operations
 */

// Type definitions for service responses
interface LoanFilters {
  status?: LoanStatus
  min_amount?: number
  max_amount?: number
  borrower_id?: string
}

interface LoanWithBorrower extends Loan {
  borrower: {
    id: string
    first_name: string
    last_name: string
    email: string
  }
}

// Create a new loan (borrower only)
export const createLoan = async (loanData: Omit<LoanInsert, 'borrower_id' | 'monthly_payment' | 'total_repayment' | 'status'>): Promise<ApiResponse<Loan>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No authenticated user')

    // Validate interest rate against Uganda's legal limit
    const { data: maxRate, error: rateError } = await supabase.rpc('get_max_interest_rate')
    if (rateError) throw rateError

    if (loanData.interest_rate <= 0) {
      throw new Error('Interest rate must be greater than 0%')
    }

    if (loanData.interest_rate > maxRate) {
      throw new Error(`Interest rate cannot exceed ${maxRate}% per month (Uganda's legal limit)`)
    }

    // Validate other loan parameters
    if (loanData.amount <= 0) {
      throw new Error('Loan amount must be greater than 0')
    }

    if (loanData.term_months <= 0 || loanData.term_months > 120) {
      throw new Error('Loan term must be between 1 and 120 months')
    }

    if (!loanData.title?.trim()) {
      throw new Error('Loan title is required')
    }

    if (!loanData.purpose?.trim()) {
      throw new Error('Loan purpose is required')
    }

    // Calculate monthly payment and total repayment
    const monthlyPayment = calculateMonthlyPayment(
      loanData.amount,
      loanData.interest_rate,
      loanData.term_months
    )
    const totalRepayment = monthlyPayment * loanData.term_months

    const { data, error } = await supabase
      .from(TABLES.LOANS)
      .insert([{
        borrower_id: user.id,
        title: loanData.title,
        description: loanData.description,
        amount: loanData.amount,
        interest_rate: loanData.interest_rate,
        term_months: loanData.term_months,
        purpose: loanData.purpose,
        monthly_payment: monthlyPayment,
        total_repayment: totalRepayment,
        status: LOAN_STATUS.DRAFT
      }])
      .select()
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Get loan by ID
export const getLoanById = async (loanId: string): Promise<ApiResponse<LoanWithBorrower>> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.LOANS)
      .select(`
        *,
        borrower:users!borrower_id(id, first_name, last_name, email),
        investments(
          id,
          amount,
          percentage,
          status,
          investor:users!investor_id(id, first_name, last_name, email)
        )
      `)
      .eq('id', loanId)
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Get all loans with filters
export const getAllLoans = async (filters: LoanFilters = {}): Promise<ApiResponse<LoanWithBorrower[]>> => {
  try {
    let query = supabase
      .from(TABLES.LOANS)
      .select(`
        *,
        borrower:users!borrower_id(id, first_name, last_name, email)
      `)
      .order('created_at', { ascending: false })

    // Apply filters
    if (filters.status) {
      query = query.eq('status', filters.status)
    }
    if (filters.borrower_id) {
      query = query.eq('borrower_id', filters.borrower_id)
    }
    if (filters.min_amount) {
      query = query.gte('amount', filters.min_amount)
    }
    if (filters.max_amount) {
      query = query.lte('amount', filters.max_amount)
    }
    if (filters.search) {
      query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%,purpose.ilike.%${filters.search}%`)
    }

    const { data, error } = await query

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Get loans by borrower
export const getLoansByBorrower = async (borrowerId) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.LOANS)
      .select('*')
      .eq('borrower_id', borrowerId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Get current user's loans (borrower)
export const getMyLoans = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No authenticated user')

    return await getLoansByBorrower(user.id)
  } catch (error) {
    return { data: null, error }
  }
}

// Update loan (borrower for draft loans, admin for any)
export const updateLoan = async (loanId, updates) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.LOANS)
      .update(updates)
      .eq('id', loanId)
      .select()
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Submit loan for approval (borrower only)
export const submitLoanForApproval = async (loanId) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.LOANS)
      .update({ status: LOAN_STATUS.PENDING })
      .eq('id', loanId)
      .select()
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Approve loan (admin only)
export const approveLoan = async (loanId) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.LOANS)
      .update({ 
        status: LOAN_STATUS.APPROVED,
        approved_at: new Date().toISOString()
      })
      .eq('id', loanId)
      .select()
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Reject loan (admin only)
export const rejectLoan = async (loanId) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.LOANS)
      .update({ 
        status: LOAN_STATUS.CANCELLED,
        // Note: In a real app, you might want a separate table for rejection reasons
      })
      .eq('id', loanId)
      .select()
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Get loan statistics
export const getLoanStatistics = async () => {
  try {
    const { data, error } = await supabase
      .from(TABLES.LOANS)
      .select('status, amount, funded_amount')

    if (error) throw error

    const stats = {
      total: data.length,
      totalAmount: data.reduce((sum, loan) => sum + parseFloat(loan.amount), 0),
      totalFunded: data.reduce((sum, loan) => sum + parseFloat(loan.funded_amount || 0), 0),
      byStatus: {
        draft: data.filter(l => l.status === LOAN_STATUS.DRAFT).length,
        pending: data.filter(l => l.status === LOAN_STATUS.PENDING).length,
        approved: data.filter(l => l.status === LOAN_STATUS.APPROVED).length,
        funded: data.filter(l => l.status === LOAN_STATUS.FUNDED).length,
        active: data.filter(l => l.status === LOAN_STATUS.ACTIVE).length,
        completed: data.filter(l => l.status === LOAN_STATUS.COMPLETED).length,
        defaulted: data.filter(l => l.status === LOAN_STATUS.DEFAULTED).length,
        cancelled: data.filter(l => l.status === LOAN_STATUS.CANCELLED).length,
      }
    }

    return { data: stats, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Delete loan (admin only or borrower for draft loans)
export const deleteLoan = async (loanId) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.LOANS)
      .delete()
      .eq('id', loanId)
      .select()
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Search loans
export const searchLoans = async (searchTerm, filters = {}) => {
  try {
    let query = supabase
      .from(TABLES.LOANS)
      .select(`
        *,
        borrower:users!borrower_id(id, first_name, last_name, email)
      `)
      .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,purpose.ilike.%${searchTerm}%`)

    if (filters.status) {
      query = query.eq('status', filters.status)
    }
    if (filters.min_amount) {
      query = query.gte('amount', filters.min_amount)
    }
    if (filters.max_amount) {
      query = query.lte('amount', filters.max_amount)
    }

    query = query.order('created_at', { ascending: false })

    const { data, error } = await query

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Helper function to calculate monthly payment
const calculateMonthlyPayment = (principal, annualRate, termMonths) => {
  const monthlyRate = annualRate / 100 / 12
  if (monthlyRate === 0) return principal / termMonths
  
  const payment = principal * (monthlyRate * Math.pow(1 + monthlyRate, termMonths)) / 
                  (Math.pow(1 + monthlyRate, termMonths) - 1)
  
  return Math.round(payment * 100) / 100 // Round to 2 decimal places
}

// Update loan status with audit trail (admin only)
export const updateLoanStatus = async (loanId, newStatus, comment = '') => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No authenticated user')

    // Get current user role
    const { data: currentUser, error: userError } = await supabase
      .from(TABLES.USERS)
      .select('role')
      .eq('id', user.id)
      .single()

    if (userError) throw userError
    if (currentUser.role !== 'admin') {
      throw new Error('Unauthorized: Only admins can update loan status')
    }

    // Get current loan data for audit trail
    const { data: currentLoan, error: loanError } = await supabase
      .from(TABLES.LOANS)
      .select('status, title, borrower_id')
      .eq('id', loanId)
      .single()

    if (loanError) throw loanError

    // Validate status transition
    const validTransitions = {
      [LOAN_STATUS.DRAFT]: [LOAN_STATUS.PENDING],
      [LOAN_STATUS.PENDING]: [LOAN_STATUS.APPROVED, LOAN_STATUS.REJECTED],
      [LOAN_STATUS.APPROVED]: [LOAN_STATUS.FUNDED],
      [LOAN_STATUS.FUNDED]: [LOAN_STATUS.ACTIVE],
      [LOAN_STATUS.ACTIVE]: [LOAN_STATUS.COMPLETED, LOAN_STATUS.DEFAULTED],
      [LOAN_STATUS.REJECTED]: [], // Final state
      [LOAN_STATUS.COMPLETED]: [], // Final state
      [LOAN_STATUS.DEFAULTED]: [] // Final state
    }

    if (!validTransitions[currentLoan.status]?.includes(newStatus)) {
      throw new Error(`Invalid status transition from ${currentLoan.status} to ${newStatus}`)
    }

    // Update loan status
    const updateData = {
      status: newStatus,
      updated_at: new Date().toISOString()
    }

    // Add status-specific timestamps
    if (newStatus === LOAN_STATUS.APPROVED) {
      updateData.approved_at = new Date().toISOString()
      updateData.approved_by = user.id
    } else if (newStatus === LOAN_STATUS.REJECTED) {
      updateData.rejected_at = new Date().toISOString()
      updateData.rejected_by = user.id
    } else if (newStatus === LOAN_STATUS.FUNDED) {
      updateData.funded_at = new Date().toISOString()
    }

    const { data: updatedLoan, error: updateError } = await supabase
      .from(TABLES.LOANS)
      .update(updateData)
      .eq('id', loanId)
      .select()
      .single()

    if (updateError) throw updateError

    // Create audit trail entry
    const { error: auditError } = await supabase
      .from('audit_logs')
      .insert([{
        admin_id: user.id,
        action: `loan_status_change`,
        resource_type: 'loan',
        resource_id: loanId,
        old_values: { status: currentLoan.status },
        new_values: { status: newStatus },
        comment: comment
      }])

    if (auditError) {
      console.error('Failed to create audit log:', auditError)
      // Don't fail the main operation if audit logging fails
    }

    return { data: updatedLoan, error: null }
  } catch (error) {
    console.error('Update loan status error:', error)
    return { data: null, error }
  }
}
