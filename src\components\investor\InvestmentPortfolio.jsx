import React, { useState, useMemo, useCallback } from 'react'
import {
  useInvestorPortfolio,
  useInvestmentPerformanceAnalytics,
  useInvestmentStatusUpdates,
  useMyDistributions,
  usePortfolioROI,
  usePortfolioPerformanceComparison
} from '../../hooks/useApi'
import { formatCurrency, formatPercentage, formatDate, getInvestmentStatusColor } from '../../services'

/**
 * Investment Portfolio Component
 * Comprehensive portfolio view for investors
 */
const InvestmentPortfolio = ({ investorId = null }) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('all')
  const [activeTab, setActiveTab] = useState('overview')

  // Fetch portfolio data
  const { 
    data: portfolio, 
    isLoading: portfolioLoading, 
    error: portfolioError 
  } = useInvestorPortfolio(investorId)

  const { 
    data: analytics, 
    isLoading: analyticsLoading 
  } = useInvestmentPerformanceAnalytics(investorId, selectedTimeframe)

  const {
    data: updates,
    isLoading: updatesLoading
  } = useInvestmentStatusUpdates(investorId, 20)

  const {
    data: distributions,
    isLoading: distributionsLoading
  } = useMyDistributions()

  const {
    data: portfolioROI
  } = usePortfolioROI(investorId, { timeframe: selectedTimeframe, includeUnrealized: true })

  const {
    data: performanceComparison
  } = usePortfolioPerformanceComparison(investorId)

  // Memoized portfolio metrics calculations
  const portfolioMetrics = useMemo(() => {
    if (!portfolio?.performance_metrics) {
      return {
        totalInvested: 0,
        totalReturns: 0,
        netGain: 0,
        roiPercentage: 0
      }
    }

    const metrics = portfolio.performance_metrics
    return {
      totalInvested: metrics.total_invested || 0,
      totalReturns: metrics.total_returns || 0,
      netGain: metrics.net_gain || 0,
      roiPercentage: metrics.roi_percentage || 0
    }
  }, [portfolio?.performance_metrics])

  // Memoized tab configuration
  const tabs = useMemo(() => [
    { id: 'overview', label: 'Overview' },
    { id: 'investments', label: 'Investments' },
    { id: 'returns', label: 'Returns' },
    { id: 'performance', label: 'Performance' },
    { id: 'activity', label: 'Activity' }
  ], [])

  // Memoized timeframe change handler
  const _handleTimeframeChange = useCallback((timeframe) => {
    setSelectedTimeframe(timeframe)
  }, [])

  // Memoized tab change handler
  const _handleTabChange = useCallback((tab) => {
    setActiveTab(tab)
  }, [])

  if (portfolioLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (portfolioError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-red-800 font-semibold">Error Loading Portfolio</h3>
        <p className="text-red-600 mt-1">{portfolioError.message}</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Portfolio Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Investment Portfolio</h1>

        {/* Key Metrics - Using memoized calculations */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="text-blue-600 text-sm font-medium">Total Invested</h3>
            <p className="text-2xl font-bold text-blue-900">
              {formatCurrency(portfolioMetrics.totalInvested)}
            </p>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <h3 className="text-green-600 text-sm font-medium">Total Returns</h3>
            <p className="text-2xl font-bold text-green-900">
              {formatCurrency(portfolioMetrics.totalReturns)}
            </p>
          </div>
          <div className="bg-purple-50 rounded-lg p-4">
            <h3 className="text-purple-600 text-sm font-medium">Net Gain/Loss</h3>
            <p className={`text-2xl font-bold ${
              portfolioMetrics.netGain >= 0 ? 'text-green-900' : 'text-red-900'
            }`}>
              {formatCurrency(portfolioMetrics.netGain)}
            </p>
          </div>
          <div className="bg-orange-50 rounded-lg p-4">
            <h3 className="text-orange-600 text-sm font-medium">ROI</h3>
            <p className={`text-2xl font-bold ${
              portfolioMetrics.roiPercentage >= 0 ? 'text-green-900' : 'text-red-900'
            }`}>
              {formatPercentage(portfolioMetrics.roiPercentage)}
            </p>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Portfolio Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Investment Summary</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Investments</span>
                      <span className="font-medium">{portfolio?.total_investments || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Active Investments</span>
                      <span className="font-medium">{portfolio?.active_investments || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Completed Investments</span>
                      <span className="font-medium">{portfolio?.completed_investments || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Active Value</span>
                      <span className="font-medium">
                        {formatCurrency(portfolio?.performance_metrics?.active_value || 0)}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Loan Status Distribution</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Active Loans</span>
                      <span className="font-medium">{portfolio?.loans_by_status?.active?.length || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Funded Loans</span>
                      <span className="font-medium">{portfolio?.loans_by_status?.funded?.length || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Completed Loans</span>
                      <span className="font-medium">{portfolio?.loans_by_status?.completed?.length || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Defaulted Loans</span>
                      <span className="font-medium text-red-600">{portfolio?.loans_by_status?.defaulted?.length || 0}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Investments Tab */}
          {activeTab === 'investments' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Active Investments</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Loan
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Percentage
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {portfolio?.investments_by_status?.active?.map((investment) => (
                      <tr key={investment.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {investment.loan?.title || 'Unknown Loan'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {formatCurrency(investment.amount)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {formatPercentage(investment.percentage)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getInvestmentStatusColor(investment.status)}`}>
                            {investment.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(investment.created_at)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Returns Tab */}
          {activeTab === 'returns' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Investment Returns</h3>

              {/* ROI Summary */}
              {portfolioROI && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="font-medium text-green-900">Total Returns</h4>
                    <p className="text-2xl font-bold text-green-900">
                      {formatCurrency(portfolioROI.basic_metrics.total_returns)}
                    </p>
                    <p className="text-sm text-green-600">
                      Realized: {formatCurrency(portfolioROI.basic_metrics.total_realized)}
                    </p>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900">ROI</h4>
                    <p className="text-2xl font-bold text-blue-900">
                      {formatPercentage(portfolioROI.basic_metrics.roi_percentage)}
                    </p>
                    <p className="text-sm text-blue-600">
                      Annualized: {formatPercentage(portfolioROI.advanced_metrics.annualized_return)}
                    </p>
                  </div>

                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h4 className="font-medium text-purple-900">Net Gain</h4>
                    <p className={`text-2xl font-bold ${
                      portfolioROI.basic_metrics.net_gain >= 0 ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {formatCurrency(portfolioROI.basic_metrics.net_gain)}
                    </p>
                    <p className="text-sm text-purple-600">
                      From {portfolioROI.basic_metrics.investment_count} investments
                    </p>
                  </div>
                </div>
              )}

              {/* Performance Comparison */}
              {performanceComparison && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Performance vs Benchmarks</h4>
                  <div className="space-y-2">
                    {Object.entries(performanceComparison.benchmarks).map(([key, benchmark]) => (
                      <div key={key} className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">{benchmark.name}</span>
                        <div className="text-right">
                          <span className="text-sm font-medium">
                            {formatPercentage(benchmark.roi_percentage)}
                          </span>
                          <span className={`ml-2 text-xs ${
                            benchmark.outperforming ? 'text-green-600' : 'text-red-600'
                          }`}>
                            ({benchmark.outperforming ? '+' : ''}{formatPercentage(benchmark.comparison)})
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Distribution History */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Recent Distributions</h4>
                {distributionsLoading ? (
                  <div className="animate-pulse space-y-3">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="bg-gray-200 h-16 rounded"></div>
                    ))}
                  </div>
                ) : distributions && distributions.length > 0 ? (
                  <div className="space-y-3">
                    {distributions.slice(0, 10).map((distribution) => (
                      <div key={distribution.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h5 className="font-medium text-gray-900">
                              {distribution.repayment?.loan?.title || 'Loan Repayment'}
                            </h5>
                            <p className="text-sm text-gray-600">
                              {formatPercentage(distribution.percentage)} of loan • {formatDate(distribution.distribution_date)}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-green-600">
                              +{formatCurrency(distribution.amount)}
                            </p>
                            <p className="text-xs text-gray-500">Distribution</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <p>No distributions received yet</p>
                    <p className="text-sm">Returns will appear here when loans are repaid</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Performance Tab */}
          {activeTab === 'performance' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Performance Analytics</h3>
                <select
                  value={selectedTimeframe}
                  onChange={(e) => setSelectedTimeframe(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="all">All Time</option>
                  <option value="1y">Last Year</option>
                  <option value="90d">Last 90 Days</option>
                  <option value="30d">Last 30 Days</option>
                </select>
              </div>

              {analyticsLoading ? (
                <div className="animate-pulse space-y-4">
                  <div className="h-32 bg-gray-200 rounded"></div>
                </div>
              ) : analytics ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Investment Summary</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Total Investments:</span>
                        <span className="font-medium">{analytics.investment_summary.total_investments}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Invested:</span>
                        <span className="font-medium">{formatCurrency(analytics.investment_summary.total_invested)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Average Investment:</span>
                        <span className="font-medium">{formatCurrency(analytics.investment_summary.average_investment)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Returns Summary</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Total Returns:</span>
                        <span className="font-medium">{formatCurrency(analytics.returns_summary.total_returns)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Return Count:</span>
                        <span className="font-medium">{analytics.returns_summary.return_count}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Average Return:</span>
                        <span className="font-medium">{formatCurrency(analytics.returns_summary.average_return)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
            </div>
          )}

          {/* Activity Tab */}
          {activeTab === 'activity' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
              {updatesLoading ? (
                <div className="animate-pulse space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 rounded"></div>
                  ))}
                </div>
              ) : updates && updates.length > 0 ? (
                <div className="space-y-3">
                  {updates.map((update) => (
                    <div key={update.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900">{update.title}</h4>
                          <p className="text-sm text-gray-600">{update.description}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">
                            {formatCurrency(Math.abs(update.amount))}
                          </p>
                          <p className="text-xs text-gray-500">{formatDate(update.date)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No recent activity found.
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default InvestmentPortfolio
