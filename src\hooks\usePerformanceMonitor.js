import { useEffect, useRef, useCallback } from 'react'

/**
 * Performance monitoring hook for tracking component render times and performance metrics
 * Provides insights into component performance and helps identify optimization opportunities
 */
export function usePerformanceMonitor(componentName, options = {}) {
  const {
    enabled = import.meta.env.DEV,
    logThreshold = 16, // Log renders that take longer than 16ms (60fps threshold)
    trackRenders = true,
    trackMounts = true
  } = options

  const renderStartTime = useRef(null)
  const renderCount = useRef(0)
  const mountTime = useRef(null)

  // Track component mount time
  useEffect(() => {
    if (!enabled || !trackMounts) return

    mountTime.current = performance.now()
    console.log(`🚀 [Performance] ${componentName} mounted at ${mountTime.current.toFixed(2)}ms`)

    return () => {
      if (mountTime.current) {
        const unmountTime = performance.now()
        const totalMountTime = unmountTime - mountTime.current
        console.log(`🔄 [Performance] ${componentName} unmounted after ${totalMountTime.toFixed(2)}ms`)
      }
    }
  }, [componentName, enabled, trackMounts])

  // Track render performance
  useEffect(() => {
    if (!enabled || !trackRenders) return

    renderCount.current += 1
    
    if (renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current
      
      if (renderTime > logThreshold) {
        console.warn(`⚠️ [Performance] ${componentName} render #${renderCount.current} took ${renderTime.toFixed(2)}ms (above ${logThreshold}ms threshold)`)
      } else {
        console.log(`✅ [Performance] ${componentName} render #${renderCount.current} took ${renderTime.toFixed(2)}ms`)
      }
    }
  })

  // Start timing before render
  if (enabled && trackRenders) {
    renderStartTime.current = performance.now()
  }

  // Measure specific operations
  const measureOperation = useCallback((operationName, operation) => {
    if (!enabled) return operation()

    const startTime = performance.now()
    const result = operation()
    const endTime = performance.now()
    const duration = endTime - startTime

    if (duration > 1) { // Only log operations that take more than 1ms
      console.log(`⏱️ [Performance] ${componentName}.${operationName} took ${duration.toFixed(2)}ms`)
    }

    return result
  }, [componentName, enabled])

  // Measure async operations
  const measureAsyncOperation = useCallback(async (operationName, operation) => {
    if (!enabled) return await operation()

    const startTime = performance.now()
    const result = await operation()
    const endTime = performance.now()
    const duration = endTime - startTime

    if (duration > 1) {
      console.log(`⏱️ [Performance] ${componentName}.${operationName} (async) took ${duration.toFixed(2)}ms`)
    }

    return result
  }, [componentName, enabled])

  // Track memory usage (if available)
  const trackMemoryUsage = useCallback(() => {
    if (!enabled || !performance.memory) return null

    const memory = performance.memory
    return {
      usedJSHeapSize: (memory.usedJSHeapSize / 1048576).toFixed(2), // MB
      totalJSHeapSize: (memory.totalJSHeapSize / 1048576).toFixed(2), // MB
      jsHeapSizeLimit: (memory.jsHeapSizeLimit / 1048576).toFixed(2) // MB
    }
  }, [enabled])

  // Log performance summary
  const logPerformanceSummary = useCallback(() => {
    if (!enabled) return

    const memory = trackMemoryUsage()
    console.group(`📊 [Performance Summary] ${componentName}`)
    console.log(`Total renders: ${renderCount.current}`)
    if (memory) {
      console.log(`Memory usage: ${memory.usedJSHeapSize}MB / ${memory.totalJSHeapSize}MB`)
    }
    console.groupEnd()
  }, [componentName, enabled, trackMemoryUsage])

  return {
    measureOperation,
    measureAsyncOperation,
    trackMemoryUsage,
    logPerformanceSummary,
    renderCount: renderCount.current
  }
}

/**
 * Hook for tracking API call performance
 */
export function useApiPerformanceMonitor(apiName, options = {}) {
  const { enabled = import.meta.env.DEV } = options

  const trackApiCall = useCallback(async (operation) => {
    if (!enabled) return await operation()

    const startTime = performance.now()
    try {
      const result = await operation()
      const endTime = performance.now()
      const duration = endTime - startTime

      console.log(`🌐 [API Performance] ${apiName} completed in ${duration.toFixed(2)}ms`)
      return result
    } catch (error) {
      const endTime = performance.now()
      const duration = endTime - startTime

      console.error(`❌ [API Performance] ${apiName} failed after ${duration.toFixed(2)}ms:`, error)
      throw error
    }
  }, [apiName, enabled])

  return { trackApiCall }
}

/**
 * Hook for tracking table rendering performance with large datasets
 */
export function useTablePerformanceMonitor(tableName, dataLength, options = {}) {
  const { enabled = import.meta.env.DEV, threshold = 100 } = options

  useEffect(() => {
    if (!enabled) return

    if (dataLength > threshold) {
      console.warn(`📋 [Table Performance] ${tableName} rendering ${dataLength} rows (above ${threshold} threshold)`)
    } else {
      console.log(`📋 [Table Performance] ${tableName} rendering ${dataLength} rows`)
    }
  }, [tableName, dataLength, enabled, threshold])

  const measureRowRender = useCallback((rowIndex, operation) => {
    if (!enabled) return operation()

    const startTime = performance.now()
    const result = operation()
    const endTime = performance.now()
    const duration = endTime - startTime

    if (duration > 5) { // Log slow row renders
      console.warn(`🐌 [Table Performance] ${tableName} row ${rowIndex} render took ${duration.toFixed(2)}ms`)
    }

    return result
  }, [tableName, enabled])

  return { measureRowRender }
}

export default usePerformanceMonitor
