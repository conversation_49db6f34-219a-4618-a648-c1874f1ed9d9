import React, { useState } from 'react'
import { useAuth } from '../../hooks/useAuth'
import { useLoans } from '../../hooks/useApi'
import { FileText, DollarSign, Calendar, Clock, CheckCircle, AlertCircle } from 'lucide-react'

const MyLoans = () => {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('active')
  const { data: allLoans, isLoading } = useLoans({ borrower_id: user?.id })

  // Group loans by status
  const loans = {
    active: allLoans?.filter(loan => loan.status === 'active') || [],
    completed: allLoans?.filter(loan => loan.status === 'completed') || [],
    pending: allLoans?.filter(loan => loan.status === 'pending') || []
  }

  // Helper functions

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <Clock className="h-5 w-5 text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'pending':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      default:
        return <FileText className="h-5 w-5 text-secondary-500" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-secondary-100 text-secondary-800'
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-UG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const tabs = [
    { id: 'active', name: 'Active Loans', count: loans.active.length },
    { id: 'completed', name: 'Completed Loans', count: loans.completed.length },
    { id: 'pending', name: 'Pending Applications', count: loans.pending.length },
  ]

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">My Loans</h1>
        <p className="text-secondary-600 mt-2">
          View and manage all your loans and applications
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-secondary-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
              }`}
            >
              {tab.name} ({tab.count})
            </button>
          ))}
        </nav>
      </div>

      {/* Loan Cards */}
      <div className="space-y-6">
        {loans[activeTab].length === 0 ? (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-secondary-400" />
            <h3 className="mt-2 text-sm font-medium text-secondary-900">No loans found</h3>
            <p className="mt-1 text-sm text-secondary-500">
              {activeTab === 'active' && "You don't have any active loans."}
              {activeTab === 'completed' && "You haven't completed any loans yet."}
              {activeTab === 'pending' && "You don't have any pending applications."}
            </p>
          </div>
        ) : (
          loans[activeTab].map((loan) => (
            <div key={loan.id} className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(loan.status)}
                  <h3 className="text-lg font-medium text-secondary-900">{loan.title}</h3>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(loan.status)}`}>
                  {loan.status.charAt(0).toUpperCase() + loan.status.slice(1)}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                  <p className="text-sm font-medium text-secondary-500">Loan Amount</p>
                  <p className="text-lg font-semibold text-secondary-900">{formatCurrency(loan.amount)}</p>
                </div>
                {loan.status === 'active' && (
                  <div>
                    <p className="text-sm font-medium text-secondary-500">Remaining Balance</p>
                    <p className="text-lg font-semibold text-secondary-900">{formatCurrency(loan.remaining_balance)}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-secondary-500">Interest Rate</p>
                  <p className="text-lg font-semibold text-secondary-900">{loan.interest_rate || 0}%</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-secondary-500">Term</p>
                  <p className="text-lg font-semibold text-secondary-900">{loan.term_months || 0} months</p>
                </div>
              </div>

              {loan.status === 'active' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm font-medium text-secondary-500">Monthly Payment</p>
                    <p className="text-lg font-semibold text-secondary-900">{formatCurrency(loan.monthly_payment)}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-secondary-500">Next Payment Due</p>
                    <p className="text-lg font-semibold text-secondary-900">{loan.next_payment_date ? formatDate(loan.next_payment_date) : 'N/A'}</p>
                  </div>
                </div>
              )}

              <div className="border-t border-secondary-200 pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-secondary-500">
                      Purpose: {loan.purpose} • Disbursed: {formatDate(loan.disbursedDate)}
                      {loan.completedDate && ` • Completed: ${formatDate(loan.completedDate)}`}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <button className="text-primary-600 hover:text-primary-900 text-sm font-medium">
                      View Details
                    </button>
                    {loan.status === 'active' && (
                      <button className="text-primary-600 hover:text-primary-900 text-sm font-medium">
                        Make Payment
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}

export default MyLoans
