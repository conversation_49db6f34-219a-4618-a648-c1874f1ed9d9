import React, { useEffect, useRef } from 'react'
import { AlertTriangle, X, Check, Trash2, UserX, FileX } from 'lucide-react'
import { ButtonLoading } from './LoadingStates'

/**
 * Confirmation Dialog Component
 * Provides accessible confirmation dialogs for destructive actions
 */
const ConfirmationDialog = ({
  isOpen = false,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'warning', // 'warning', 'danger', 'info'
  loading = false,
  disabled = false,
  className = ''
}) => {
  const dialogRef = useRef(null)
  const confirmButtonRef = useRef(null)
  const cancelButtonRef = useRef(null)

  // Focus management
  useEffect(() => {
    if (isOpen) {
      // Focus the cancel button by default for safety
      cancelButtonRef.current?.focus()
    }
  }, [isOpen])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen && !loading) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      // Prevent body scroll
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, loading, onClose])

  // Handle tab trapping
  const handleKeyDown = (e) => {
    if (e.key === 'Tab') {
      const focusableElements = dialogRef.current?.querySelectorAll(
        'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
      )
      
      if (focusableElements && focusableElements.length > 0) {
        const firstElement = focusableElements[0]
        const lastElement = focusableElements[focusableElements.length - 1]

        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault()
            lastElement.focus()
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault()
            firstElement.focus()
          }
        }
      }
    }
  }

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget && !loading) {
      onClose()
    }
  }

  const handleConfirm = () => {
    if (!loading && !disabled) {
      onConfirm()
    }
  }

  const handleCancel = () => {
    if (!loading) {
      onClose()
    }
  }

  // Icon mapping
  const iconMap = {
    warning: AlertTriangle,
    danger: Trash2,
    info: Check,
    delete: Trash2,
    deactivate: UserX,
    remove: FileX
  }

  const Icon = iconMap[type] || AlertTriangle

  // Color schemes
  const colorSchemes = {
    warning: {
      icon: 'text-warning-600',
      bg: 'bg-warning-50',
      border: 'border-warning-200',
      confirmButton: 'bg-warning-600 hover:bg-warning-700 focus:ring-warning-500',
      title: 'text-warning-900'
    },
    danger: {
      icon: 'text-danger-600',
      bg: 'bg-danger-50',
      border: 'border-danger-200',
      confirmButton: 'bg-danger-600 hover:bg-danger-700 focus:ring-danger-500',
      title: 'text-danger-900'
    },
    info: {
      icon: 'text-primary-600',
      bg: 'bg-primary-50',
      border: 'border-primary-200',
      confirmButton: 'bg-primary-600 hover:bg-primary-700 focus:ring-primary-500',
      title: 'text-primary-900'
    },
    delete: {
      icon: 'text-danger-600',
      bg: 'bg-danger-50',
      border: 'border-danger-200',
      confirmButton: 'bg-danger-600 hover:bg-danger-700 focus:ring-danger-500',
      title: 'text-danger-900'
    },
    deactivate: {
      icon: 'text-warning-600',
      bg: 'bg-warning-50',
      border: 'border-warning-200',
      confirmButton: 'bg-warning-600 hover:bg-warning-700 focus:ring-warning-500',
      title: 'text-warning-900'
    },
    remove: {
      icon: 'text-danger-600',
      bg: 'bg-danger-50',
      border: 'border-danger-200',
      confirmButton: 'bg-danger-600 hover:bg-danger-700 focus:ring-danger-500',
      title: 'text-danger-900'
    }
  }

  const colors = colorSchemes[type] || colorSchemes.warning

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 z-50 overflow-y-auto"
      aria-labelledby="dialog-title"
      role="dialog"
      aria-modal="true"
    >
      {/* Backdrop */}
      <div
        className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
        onClick={handleBackdropClick}
      >
        <div
          className="fixed inset-0 bg-secondary-500 bg-opacity-75 transition-opacity"
          aria-hidden="true"
        ></div>

        {/* Center the dialog */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
          &#8203;
        </span>

        {/* Dialog panel */}
        <div
          ref={dialogRef}
          className={`
            inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all 
            sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6
            ${className}
          `}
          onKeyDown={handleKeyDown}
        >
          <div className="sm:flex sm:items-start">
            {/* Icon */}
            <div className={`
              mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10
              ${colors.bg} ${colors.border} border
            `}>
              <Icon className={`h-6 w-6 ${colors.icon}`} aria-hidden="true" />
            </div>

            {/* Content */}
            <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
              <h3
                id="dialog-title"
                className={`text-lg leading-6 font-medium ${colors.title}`}
              >
                {title}
              </h3>
              <div className="mt-2">
                <p className="text-sm text-secondary-500">
                  {message}
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
            <ButtonLoading
              ref={confirmButtonRef}
              loading={loading}
              loadingText="Processing..."
              disabled={disabled}
              onClick={handleConfirm}
              className={`
                w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white 
                focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm
                ${colors.confirmButton}
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              {confirmText}
            </ButtonLoading>

            <button
              ref={cancelButtonRef}
              type="button"
              disabled={loading}
              onClick={handleCancel}
              className="
                mt-3 w-full inline-flex justify-center rounded-md border border-secondary-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-secondary-700 
                hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:w-auto sm:text-sm
                disabled:opacity-50 disabled:cursor-not-allowed
              "
            >
              {cancelText}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}



export default ConfirmationDialog
