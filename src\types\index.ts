/**
 * Ontare Types Index
 * Central export point for all TypeScript types
 * 
 * This file provides a single source of truth for all database and application types.
 * All components should import types from this file, not from individual type files.
 */

// Re-export all database types
export type {
  Database,
  Json,
  Tables,
  TablesInsert,
  TablesUpdate,
  Enums,
  CompositeTypes
} from './database'

export { Constants } from './database'

// Convenient type aliases for common database entities
export type User = Tables<'users'>
export type UserInsert = TablesInsert<'users'>
export type UserUpdate = TablesUpdate<'users'>

export type Loan = Tables<'loans'>
export type LoanInsert = TablesInsert<'loans'>
export type LoanUpdate = TablesUpdate<'loans'>

export type Investment = Tables<'investments'>
export type InvestmentInsert = TablesInsert<'investments'>
export type InvestmentUpdate = TablesUpdate<'investments'>

export type Repayment = Tables<'repayments'>
export type RepaymentInsert = TablesInsert<'repayments'>
export type RepaymentUpdate = TablesUpdate<'repayments'>

export type Wallet = Tables<'wallets'>
export type WalletInsert = TablesInsert<'wallets'>
export type WalletUpdate = TablesUpdate<'wallets'>

export type WalletTransaction = Tables<'wallet_transactions'>
export type WalletTransactionInsert = TablesInsert<'wallet_transactions'>
export type WalletTransactionUpdate = TablesUpdate<'wallet_transactions'>

export type InvestorDistribution = Tables<'investor_distributions'>
export type InvestorDistributionInsert = TablesInsert<'investor_distributions'>
export type InvestorDistributionUpdate = TablesUpdate<'investor_distributions'>

export type AuditLog = Tables<'audit_logs'>
export type AuditLogInsert = TablesInsert<'audit_logs'>
export type AuditLogUpdate = TablesUpdate<'audit_logs'>

// Enum type aliases
export type UserRole = Enums<'user_role'>
export type LoanStatus = Enums<'loan_status'>
export type InvestmentStatus = Enums<'investment_status'>
export type RepaymentStatus = Enums<'repayment_status'>
export type TransactionType = Enums<'transaction_type'>
export type WalletTransactionStatus = Enums<'wallet_transaction_status'>

// Application-specific types
export interface ApiResponse<T = any> {
  data?: T
  error?: {
    message: string
    code?: string
    details?: any
  }
  success: boolean
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface ValidationError {
  field: string
  message: string
}

export interface FormValidationResult {
  isValid: boolean
  errors: ValidationError[]
}

// Dashboard and analytics types
export interface DashboardStats {
  totalLoans: number
  totalInvestments: number
  totalUsers: number
  totalRepayments: number
  platformValue: number
  activeLoans: number
  completedLoans: number
  defaultedLoans: number
}

export interface LoanStatistics {
  totalAmount: number
  averageAmount: number
  averageInterestRate: number
  averageTermMonths: number
  statusDistribution: Record<LoanStatus, number>
}

export interface InvestmentStatistics {
  totalInvested: number
  averageInvestment: number
  totalReturns: number
  averageROI: number
  statusDistribution: Record<InvestmentStatus, number>
}

export interface UserStatistics {
  totalUsers: number
  activeUsers: number
  roleDistribution: Record<UserRole, number>
  newUsersThisMonth: number
}

// Portfolio and performance types
export interface PortfolioSummary {
  totalInvested: number
  currentValue: number
  totalReturns: number
  roi: number
  activeInvestments: number
  completedInvestments: number
}

export interface InvestmentPerformance {
  investmentId: string
  loanTitle: string
  amountInvested: number
  currentValue: number
  returns: number
  roi: number
  status: InvestmentStatus
  investedAt: string
  expectedCompletion?: string
}

// Form and UI types
export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
}

export interface TableColumn<T = any> {
  key: keyof T | string
  label: string
  sortable?: boolean
  render?: (value: any, row: T) => React.ReactNode
  className?: string
}

export interface FilterOption {
  key: string
  label: string
  type: 'select' | 'date' | 'number' | 'text'
  options?: SelectOption[]
  placeholder?: string
}

// Authentication and user context types
export interface AuthUser extends User {
  // Additional computed properties
  fullName: string
  isAdmin: boolean
  isInvestor: boolean
  isBorrower: boolean
}

export interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  error: string | null
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, userData: Partial<UserInsert>) => Promise<void>
  signOut: () => Promise<void>
  updateProfile: (updates: UserUpdate) => Promise<void>
  refreshUser: () => Promise<void>
}

// Toast notification types
export interface ToastMessage {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

export interface ToastContextType {
  toasts: ToastMessage[]
  addToast: (toast: Omit<ToastMessage, 'id'>) => void
  removeToast: (id: string) => void
  clearToasts: () => void
}

// API hook types
export interface UseApiOptions {
  enabled?: boolean
  refetchInterval?: number
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
}

export interface UseApiResult<T = any> {
  data: T | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  mutate: (data: T) => void
}

// Loan fragmentation types
export interface FragmentationSuggestion {
  investorId: string
  investorName: string
  suggestedAmount: number
  availableBalance: number
  riskScore: number
  previousInvestments: number
}

export interface FragmentationResult {
  success: boolean
  allocations: Array<{
    investorId: string
    amount: number
    percentage: number
  }>
  totalAllocated: number
  remainingAmount: number
}

// ROI and analytics calculation types
export interface ROICalculation {
  initialInvestment: number
  currentValue: number
  totalReturns: number
  roi: number
  annualizedReturn: number
  investmentPeriodDays: number
}

export interface PortfolioComparison {
  userPortfolio: PortfolioSummary
  platformAverage: PortfolioSummary
  performanceRank: number
  totalInvestors: number
}

// Report generation types
export interface ReportOptions {
  startDate: string
  endDate: string
  format: 'pdf' | 'excel' | 'csv'
  includeCharts?: boolean
  includeDetails?: boolean
}

export interface ReportData {
  title: string
  generatedAt: string
  period: string
  summary: Record<string, any>
  details: any[]
  charts?: any[]
}

// Error handling types
export interface AppError extends Error {
  code?: string
  statusCode?: number
  details?: any
}

export interface ErrorBoundaryState {
  hasError: boolean
  error?: AppError
  errorInfo?: any
}

// Performance monitoring types
export interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  metadata?: Record<string, any>
}

export interface ComponentPerformance {
  componentName: string
  renderTime: number
  mountTime?: number
  updateCount: number
  lastUpdate: number
}
