import React, { useState } from 'react'
import { BarChart3, TrendingUp, TrendingDown, Calendar } from 'lucide-react'
import { useAuth } from '../../../hooks/useAuth'
import { useInvestments } from '../../../hooks/useApi'

const TrendsPage = () => {
  const [timeframe, setTimeframe] = useState('6m')
  const { user } = useAuth()
  const { data: investments, isLoading } = useInvestments({ investor_id: user?.id })

  // Calculate trends from real data
  const monthlyData = investments?.reduce((acc, inv) => {
    const month = new Date(inv.created_at).toLocaleDateString('en-US', { month: 'short' })
    const existing = acc.find(item => item.month === month)
    if (existing) {
      existing.investment += inv.amount || 0
      existing.return += inv.returns || 0
    } else {
      acc.push({
        month,
        investment: inv.amount || 0,
        return: inv.returns || 0
      })
    }
    return acc
  }, []) || []

  const avgReturn = investments?.length ?
    investments.reduce((sum, inv) => sum + (inv.returns || 0), 0) / investments.length : 0

  const trendsData = {
    monthlyReturns: monthlyData.slice(0, 6),
    marketTrends: {
      avgMarketReturn: 1.4, // This would come from external market data
      yourAvgReturn: avgReturn,
      marketVolatility: 15.2, // This would come from external market data
      yourVolatility: 0 // Would need historical data to calculate
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  const getReturnTrend = (current, previous) => {
    if (current > previous) return { direction: 'up', color: 'green' }
    if (current < previous) return { direction: 'down', color: 'red' }
    return { direction: 'stable', color: 'gray' }
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Investment Trends</h1>
        <p className="text-secondary-600 mt-2">
          Track market trends and compare your performance with market averages
        </p>
      </div>

      {/* Timeframe Selector */}
      <div className="mb-6">
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          <option value="3m">Last 3 months</option>
          <option value="6m">Last 6 months</option>
          <option value="1y">Last year</option>
          <option value="2y">Last 2 years</option>
        </select>
      </div>

      {/* Market Comparison */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg">
              <BarChart3 className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Market Avg Return</p>
              <p className="text-2xl font-bold text-secondary-900">{trendsData.marketTrends.avgMarketReturn}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-lg">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Your Avg Return</p>
              <p className="text-2xl font-bold text-secondary-900">{trendsData.marketTrends.yourAvgReturn}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-orange-100 p-3 rounded-lg">
              <TrendingDown className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Market Volatility</p>
              <p className="text-2xl font-bold text-secondary-900">{trendsData.marketTrends.marketVolatility}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-lg">
              <Calendar className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Your Volatility</p>
              <p className="text-2xl font-bold text-secondary-900">{trendsData.marketTrends.yourVolatility}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Monthly Performance Trend */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Performance Trend</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Month</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Return (%)</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Investment</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Trend</th>
              </tr>
            </thead>
            <tbody>
              {trendsData.monthlyReturns.map((item, index) => {
                const previousReturn = index > 0 ? trendsData.monthlyReturns[index - 1].return : item.return
                const trend = getReturnTrend(item.return, previousReturn)
                
                return (
                  <tr key={item.month} className="border-b border-gray-100">
                    <td className="py-3 px-4 text-sm text-gray-900">{item.month}</td>
                    <td className="py-3 px-4 text-sm font-medium text-gray-900">{item.return}%</td>
                    <td className="py-3 px-4 text-sm text-gray-900">${item.investment.toLocaleString()}</td>
                    <td className="py-3 px-4">
                      {trend.direction === 'up' && (
                        <TrendingUp className={`h-4 w-4 text-${trend.color}-600`} />
                      )}
                      {trend.direction === 'down' && (
                        <TrendingDown className={`h-4 w-4 text-${trend.color}-600`} />
                      )}
                      {trend.direction === 'stable' && (
                        <div className={`h-1 w-4 bg-${trend.color}-400 rounded`}></div>
                      )}
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Performance vs Market */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance vs Market</h3>
        <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
          <p className="text-gray-500">Performance comparison chart will be implemented here</p>
        </div>
      </div>

      {/* Trend Insights */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Trend Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-green-50 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Outperforming Market</h4>
            <p className="text-sm text-green-700">
              Your portfolio is performing slightly below market average but with lower volatility
            </p>
          </div>
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Consistent Growth</h4>
            <p className="text-sm text-blue-700">
              Your investments show consistent month-over-month growth with manageable risk
            </p>
          </div>
          <div className="p-4 bg-yellow-50 rounded-lg">
            <h4 className="font-medium text-yellow-900 mb-2">Seasonal Patterns</h4>
            <p className="text-sm text-yellow-700">
              April shows the highest returns, consider increasing allocation during this period
            </p>
          </div>
          <div className="p-4 bg-purple-50 rounded-lg">
            <h4 className="font-medium text-purple-900 mb-2">Risk Management</h4>
            <p className="text-sm text-purple-700">
              Your volatility is 15% lower than market average, indicating good risk management
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TrendsPage
