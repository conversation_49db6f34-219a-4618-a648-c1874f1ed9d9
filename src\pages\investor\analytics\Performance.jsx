import React, { useState } from 'react'
import { TrendingUp, DollarSign, Percent, Calendar } from 'lucide-react'
import { useAuth } from '../../../hooks/useAuth'

const PerformancePage = () => {
  const [timeframe, setTimeframe] = useState('30d')
  const { user: _user } = useAuth()

  // Mock data - will be replaced with real data from Supabase
  const performanceStats = {
    totalReturns: 12500,
    roi: 8.5,
    monthlyReturn: 1.2,
    yearlyProjection: 15.8
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Performance Analytics</h1>
        <p className="text-secondary-600 mt-2">
          Track your investment performance and returns over time
        </p>
      </div>

      {/* Timeframe Selector */}
      <div className="mb-6">
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
          <option value="1y">Last year</option>
        </select>
      </div>

      {/* Performance Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Total Returns</p>
              <p className="text-2xl font-bold text-secondary-900">
                ${performanceStats.totalReturns.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">ROI</p>
              <p className="text-2xl font-bold text-secondary-900">{performanceStats.roi}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-lg">
              <Percent className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Monthly Return</p>
              <p className="text-2xl font-bold text-secondary-900">{performanceStats.monthlyReturn}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-orange-100 p-3 rounded-lg">
              <Calendar className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Yearly Projection</p>
              <p className="text-2xl font-bold text-secondary-900">{performanceStats.yearlyProjection}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Chart Placeholder */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Over Time</h3>
        <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
          <p className="text-gray-500">Performance chart will be implemented here</p>
        </div>
      </div>

      {/* Recent Performance Summary */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Performance Summary</h3>
        <div className="space-y-4">
          <div className="flex justify-between items-center py-2 border-b border-gray-200">
            <span className="text-sm text-gray-600">This Month</span>
            <span className="text-sm font-medium text-green-600">+$1,250 (1.2%)</span>
          </div>
          <div className="flex justify-between items-center py-2 border-b border-gray-200">
            <span className="text-sm text-gray-600">Last Month</span>
            <span className="text-sm font-medium text-green-600">+$1,180 (1.1%)</span>
          </div>
          <div className="flex justify-between items-center py-2 border-b border-gray-200">
            <span className="text-sm text-gray-600">3 Months Ago</span>
            <span className="text-sm font-medium text-green-600">+$1,320 (1.3%)</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PerformancePage
