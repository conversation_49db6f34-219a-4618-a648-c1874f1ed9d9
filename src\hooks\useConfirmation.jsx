import React from 'react'
import ConfirmationDialog from '../components/common/ConfirmationDialog'

/**
 * Hook for managing confirmation dialogs
 */
export const useConfirmation = () => {
  const [dialog, setDialog] = React.useState({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    type: 'warning',
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    loading: false
  })

  const showConfirmation = (options) => {
    setDialog({
      isOpen: true,
      title: options.title || 'Confirm Action',
      message: options.message || 'Are you sure you want to proceed?',
      onConfirm: options.onConfirm || (() => {}),
      type: options.type || 'warning',
      confirmText: options.confirmText || 'Confirm',
      cancelText: options.cancelText || 'Cancel',
      loading: false
    })
  }

  const hideConfirmation = () => {
    setDialog(prev => ({ ...prev, isOpen: false, loading: false }))
  }

  const setLoading = (loading) => {
    setDialog(prev => ({ ...prev, loading }))
  }

  const handleConfirm = async () => {
    setLoading(true)
    try {
      await dialog.onConfirm()
      hideConfirmation()
    } catch (error) {
      setLoading(false)
      // Let the parent handle the error
      throw error
    }
  }

  const ConfirmationComponent = () => (
    <ConfirmationDialog
      isOpen={dialog.isOpen}
      onClose={hideConfirmation}
      onConfirm={handleConfirm}
      title={dialog.title}
      message={dialog.message}
      type={dialog.type}
      confirmText={dialog.confirmText}
      cancelText={dialog.cancelText}
      loading={dialog.loading}
    />
  )

  return {
    showConfirmation,
    hideConfirmation,
    setLoading,
    ConfirmationComponent
  }
}
