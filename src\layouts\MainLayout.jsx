import { Outlet, Link, useLocation } from 'react-router-dom'
import { useState, useMemo, useCallback, memo } from 'react'
import { useAuth } from '../hooks/useAuth'
import { usePerformanceMonitor } from '../hooks/usePerformanceMonitor'
import Breadcrumbs from '../components/common/Breadcrumbs'
import Logo from '../components/common/Logo'
import {
  Menu,
  X,
  Home,
  Users,
  DollarSign,
  FileText,
  Settings,
  LogOut,
  Bell,
  User,
  BarChart3,
  TrendingUp,
  PieChart,
  AlertTriangle,
  Download,
  Wrench,
  ChevronDown,
  ChevronRight,
  CreditCard
} from 'lucide-react'

// Static navigation configuration moved outside component to prevent recreation
const NAVIGATION_CONFIG = {
  admin: [
    { name: 'Dashboard', href: '/app/admin', icon: Home },
    { name: 'User Management', href: '/app/admin/users', icon: Users },
    { name: 'Loan Management', href: '/app/admin/loans', icon: FileText },
    { name: 'Investment Management', href: '/app/admin/investments', icon: DollarSign },
    { name: 'Repayment Management', href: '/app/admin/repayments', icon: CreditCard },
    {
      name: 'Analytics',
      icon: BarChart3,
      isDropdown: true,
      children: [
        { name: 'Platform Analytics', href: '/app/admin/analytics/platform', icon: BarChart3 },
        { name: 'User Analytics', href: '/app/admin/analytics/users', icon: Users },
        { name: 'Financial Analytics', href: '/app/admin/analytics/financial', icon: DollarSign },
        { name: 'Reports', href: '/app/admin/analytics/reports', icon: Download },
      ]
    },
    {
      name: 'System Tools',
      icon: Wrench,
      isDropdown: true,
      children: [
        { name: 'API Testing', href: '/app/admin/tools/api-testing', icon: Settings },
      ]
    },
    { name: 'Settings', href: '/app/admin/settings', icon: Settings },
  ],
  investor: [
    { name: 'Dashboard', href: '/app/investor', icon: Home },
    { name: 'My Portfolio', href: '/app/investor/portfolio', icon: DollarSign },
    {
      name: 'Analytics',
      icon: BarChart3,
      isDropdown: true,
      children: [
        { name: 'Performance', href: '/app/investor/analytics/performance', icon: TrendingUp },
        { name: 'Risk Analysis', href: '/app/investor/analytics/risk', icon: AlertTriangle },
        { name: 'Diversification', href: '/app/investor/analytics/diversification', icon: PieChart },
        { name: 'Trends', href: '/app/investor/analytics/trends', icon: BarChart3 },
      ]
    },
    { name: 'Settings', href: '/app/investor/settings', icon: Settings },
  ],
  borrower: [
    { name: 'Dashboard', href: '/app/borrower', icon: Home },
    { name: 'Apply for Loan', href: '/app/borrower/apply', icon: FileText },
    { name: 'My Loans', href: '/app/borrower/loans', icon: DollarSign },
    {
      name: 'Analytics',
      icon: BarChart3,
      isDropdown: true,
      children: [
        { name: 'Loan Analysis', href: '/app/borrower/analytics/loans', icon: FileText },
        { name: 'Repayment Analysis', href: '/app/borrower/analytics/repayments', icon: CreditCard },
        { name: 'Reports', href: '/app/borrower/analytics/reports', icon: Download },
      ]
    },
    { name: 'Settings', href: '/app/borrower/settings', icon: Settings },
  ],
}

const MainLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedMenus, setExpandedMenus] = useState({})
  const { user, signOut } = useAuth()
  const location = useLocation()

  // Performance monitoring for main layout
  const { measureOperation: _measureOperation, logPerformanceSummary: _logPerformanceSummary } = usePerformanceMonitor('MainLayout', {
    logThreshold: 10 // Lower threshold for critical layout component
  })

  // Memoize navigation based on user role to prevent recreation
  const navigation = useMemo(() =>
    NAVIGATION_CONFIG[user?.role] || [],
    [user?.role]
  )

  // Memoized helper functions to prevent recreation on every render
  const isActiveRoute = useCallback((href) => {
    if (href === '/app/admin' || href === '/app/investor' || href === '/app/borrower') {
      // For dashboard routes, match exactly
      return location.pathname === href
    }
    // For other routes, check if current path starts with the href
    return location.pathname.startsWith(href)
  }, [location.pathname])

  // Memoized helper function to check if dropdown has active child
  const hasActiveChild = useCallback((children) => {
    return children?.some(child => isActiveRoute(child.href))
  }, [isActiveRoute])

  // Memoized toggle dropdown function
  const toggleDropdown = useCallback((menuName) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuName]: !prev[menuName]
    }))
  }, [])

  // Memoized sidebar toggle
  const handleSidebarToggle = useCallback(() => {
    setSidebarOpen(prev => !prev)
  }, [])

  const handleSignOut = useCallback(async () => {
    await signOut()
  }, [signOut])

  // Memoized Navigation Item Component to prevent unnecessary re-renders
  const NavigationItem = memo(({ item, isMobile = false }) => {
    if (item.isDropdown) {
      const isExpanded = expandedMenus[item.name]
      const hasActive = hasActiveChild(item.children)

      return (
        <div key={item.name}>
          <button
            onClick={() => toggleDropdown(item.name)}
            className={`group flex items-center w-full px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 ${
              hasActive
                ? 'bg-cream-100/70 text-navy-800'
                : 'text-secondary-700 hover:bg-cream-100/70 hover:text-navy-800'
            }`}
          >
            <item.icon className={`mr-3 h-5 w-5 ${hasActive ? 'text-primary-600' : 'text-secondary-500 group-hover:text-primary-600'}`} />
            {item.name}
            {isExpanded ? (
              <ChevronDown className="ml-auto h-4 w-4 text-secondary-500" />
            ) : (
              <ChevronRight className="ml-auto h-4 w-4 text-secondary-500" />
            )}
          </button>
          {isExpanded && (
            <div className="ml-6 mt-2 space-y-1">
              {item.children.map((child) => {
                const isActive = isActiveRoute(child.href)
                return (
                  <Link
                    key={child.name}
                    to={child.href}
                    className={`group flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-soft'
                        : 'text-secondary-600 hover:bg-cream-50 hover:text-navy-700'
                    }`}
                    onClick={isMobile ? () => setSidebarOpen(false) : undefined}
                  >
                    <child.icon className={`mr-3 h-4 w-4 ${isActive ? 'text-white' : 'text-secondary-400 group-hover:text-primary-500'}`} />
                    {child.name}
                  </Link>
                )
              })}
            </div>
          )}
        </div>
      )
    }

    // Regular navigation item
    const isActive = isActiveRoute(item.href)
    return (
      <Link
        key={item.name}
        to={item.href}
        className={`group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 ${
          isActive
            ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-soft'
            : 'text-secondary-700 hover:bg-cream-100/70 hover:text-navy-800'
        }`}
        onClick={isMobile ? () => setSidebarOpen(false) : undefined}
      >
        <item.icon className={`mr-3 h-5 w-5 ${isActive ? 'text-white' : 'text-secondary-500 group-hover:text-primary-600'}`} />
        {item.name}
      </Link>
    )
  }, (prevProps, nextProps) => {
    // Custom comparison function for memo optimization
    return (
      prevProps.item === nextProps.item &&
      prevProps.isMobile === nextProps.isMobile
    )
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-cream-50 via-white to-primary-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-navy-900 bg-opacity-75 backdrop-blur-sm" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-72 flex-col bg-white/95 backdrop-blur-lg shadow-large">
          <div className="flex h-20 items-center justify-between px-6 border-b border-cream-200/50">
            <div className="flex items-center space-x-3">
              <Logo size="md" />
              <div>
                <h1 className="text-lg font-bold text-navy-900">Ontare</h1>
                <p className="text-xs text-secondary-600 uppercase tracking-wide">LOAN PLATFORM</p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              aria-label="Close navigation menu"
              className="text-secondary-400 hover:text-secondary-600 p-2 rounded-lg hover:bg-cream-100/50 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => (
              <NavigationItem key={item.name} item={item} isMobile={true} />
            ))}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-72 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white/80 backdrop-blur-lg border-r border-cream-200/50 shadow-soft">
          <div className="flex h-20 items-center px-6 border-b border-cream-200/50">
            <div className="flex items-center space-x-3">
              <Logo size="md" />
              <div>
                <h1 className="text-lg font-bold text-navy-900">Ontare</h1>
                <p className="text-xs text-secondary-600 uppercase tracking-wide">LOAN PLATFORM</p>
              </div>
            </div>
          </div>
          <nav className="flex-1 space-y-2 px-4 py-6">
            {navigation.map((item) => (
              <NavigationItem key={item.name} item={item} isMobile={false} />
            ))}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-72">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-20 shrink-0 items-center gap-x-4 border-b border-cream-200/50 bg-white/80 backdrop-blur-lg px-4 shadow-soft sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-secondary-700 lg:hidden hover:bg-cream-100/50 rounded-lg transition-colors"
            onClick={handleSidebarToggle}
            aria-label="Open navigation menu"
          >
            <Menu className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1"></div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications */}
              <button
                className="relative -m-2.5 p-2.5 text-secondary-400 hover:text-secondary-600 hover:bg-cream-100/50 rounded-lg transition-colors"
                aria-label="View notifications (3 unread)"
              >
                <Bell className="h-6 w-6" />
                <span
                  className="absolute -top-1 -right-1 h-4 w-4 bg-gradient-to-r from-danger-500 to-danger-600 rounded-full text-xs text-white flex items-center justify-center shadow-soft"
                  aria-hidden="true"
                >
                  3
                </span>
              </button>

              {/* Profile dropdown */}
              <div className="relative">
                <button
                  className="flex items-center gap-x-3 text-sm font-semibold text-navy-900 hover:bg-cream-100/50 px-3 py-2 rounded-xl transition-colors"
                  aria-label={`User profile: ${user?.first_name} ${user?.last_name}`}
                >
                  <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
                    <User className="h-4 w-4 text-white" />
                  </div>
                  <span className="hidden sm:block">{user?.first_name} {user?.last_name}</span>
                </button>
              </div>

              {/* Sign out */}
              <button
                onClick={handleSignOut}
                className="flex items-center gap-x-2 text-sm font-semibold text-secondary-700 hover:text-danger-600 hover:bg-danger-50/50 px-3 py-2 rounded-xl transition-colors"
                aria-label="Sign out of your account"
              >
                <LogOut className="h-5 w-5" />
                <span className="hidden sm:block">Sign out</span>
              </button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-8">
          <div className="px-4 sm:px-6 lg:px-8">
            <Breadcrumbs />
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  )
}

export default MainLayout
