<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer circle with gradient -->
  <circle 
    cx="24" 
    cy="24" 
    r="22" 
    fill="url(#gradient1)"
    stroke="url(#gradient2)" 
    stroke-width="2"
  />
  
  <!-- Inner geometric pattern representing connection/network -->
  <path 
    d="M16 20L24 12L32 20L28 24L24 20L20 24L16 20Z" 
    fill="white" 
    fill-opacity="0.9"
  />
  <path 
    d="M16 28L24 36L32 28L28 24L24 28L20 24L16 28Z" 
    fill="white" 
    fill-opacity="0.7"
  />
  
  <!-- Central diamond representing the platform core -->
  <circle 
    cx="24" 
    cy="24" 
    r="3" 
    fill="white"
  />
  
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4f46e5" />
      <stop offset="50%" stop-color="#6366f1" />
      <stop offset="100%" stop-color="#312e81" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#fef9f3" />
      <stop offset="100%" stop-color="#f0c087" />
    </linearGradient>
  </defs>
</svg>
