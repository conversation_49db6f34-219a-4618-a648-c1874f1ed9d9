import React, { useState } from 'react'
import { FixedSizeList as List } from 'react-window'
import { ChevronDown, ChevronRight } from 'lucide-react'

/**
 * Responsive Table Component
 * Automatically switches between table and card layout based on screen size
 */
const ResponsiveTable = ({
  data = [],
  columns = [],
  loading = false,
  emptyMessage = 'No data available',
  className = '',
  onRowClick,
  expandable = false,
  renderExpandedContent,
  keyField = 'id'
}) => {
  const [expandedRows, setExpandedRows] = useState(new Set())

  const toggleRowExpansion = (rowId) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(rowId)) {
      newExpanded.delete(rowId)
    } else {
      newExpanded.add(rowId)
    }
    setExpandedRows(newExpanded)
  }

  const handleRowClick = (row, e) => {
    // Don't trigger row click if clicking on interactive elements
    if (e.target.closest('button, select, input, a')) {
      return
    }
    if (onRowClick) {
      onRowClick(row)
    }
  }

  if (loading) {
    return (
      <div className="animate-pulse">
        {/* Desktop skeleton */}
        <div className="hidden md:block overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table className="min-w-full divide-y divide-secondary-300">
            <thead className="bg-secondary-50">
              <tr>
                {columns.map((_, index) => (
                  <th key={index} className="px-6 py-3">
                    <div className="h-4 bg-secondary-300 rounded"></div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-secondary-200">
              {Array.from({ length: 5 }).map((_, rowIndex) => (
                <tr key={rowIndex}>
                  {columns.map((_, colIndex) => (
                    <td key={colIndex} className="px-6 py-4">
                      <div className="h-4 bg-secondary-200 rounded"></div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile skeleton */}
        <div className="md:hidden space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="bg-white p-4 rounded-lg shadow border">
              <div className="space-y-3">
                <div className="h-4 bg-secondary-300 rounded w-3/4"></div>
                <div className="h-3 bg-secondary-200 rounded w-1/2"></div>
                <div className="h-3 bg-secondary-200 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-secondary-500 text-lg">{emptyMessage}</p>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Desktop Table View */}
      <div className="hidden md:block overflow-x-auto shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table className="min-w-full divide-y divide-secondary-300">
          <thead className="bg-secondary-50">
            <tr>
              {expandable && (
                <th className="w-8 px-3 py-3"></th>
              )}
              {columns.map((column, index) => (
                <th
                  key={column.key || index}
                  className={`px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider ${column.headerClassName || ''}`}
                >
                  {column.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-secondary-200">
            {data.map((row, rowIndex) => {
              const rowId = row[keyField] || rowIndex
              const isExpanded = expandedRows.has(rowId)
              
              return (
                <React.Fragment key={rowId}>
                  <tr 
                    className={`hover:bg-secondary-50 ${onRowClick ? 'cursor-pointer' : ''}`}
                    onClick={(e) => handleRowClick(row, e)}
                  >
                    {expandable && (
                      <td className="px-3 py-4">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            toggleRowExpansion(rowId)
                          }}
                          className="text-secondary-400 hover:text-secondary-600"
                          aria-label={isExpanded ? 'Collapse row' : 'Expand row'}
                        >
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </button>
                      </td>
                    )}
                    {columns.map((column, colIndex) => (
                      <td
                        key={column.key || colIndex}
                        className={`px-6 py-4 whitespace-nowrap ${column.cellClassName || ''}`}
                      >
                        {column.render ? column.render(row, rowIndex) : row[column.key]}
                      </td>
                    ))}
                  </tr>
                  {expandable && isExpanded && renderExpandedContent && (
                    <tr>
                      <td colSpan={columns.length + 1} className="px-6 py-4 bg-secondary-50">
                        {renderExpandedContent(row, rowIndex)}
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              )
            })}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-4">
        {data.map((row, rowIndex) => {
          const rowId = row[keyField] || rowIndex
          const isExpanded = expandedRows.has(rowId)
          
          return (
            <div
              key={rowId}
              className={`bg-white rounded-lg shadow border ${onRowClick ? 'cursor-pointer hover:shadow-md' : ''} transition-shadow duration-200`}
              onClick={(e) => handleRowClick(row, e)}
            >
              <div className="p-4">
                {/* Primary content */}
                <div className="space-y-3">
                  {columns
                    .filter(col => col.showInMobile !== false)
                    .slice(0, 3) // Show first 3 columns in mobile by default
                    .map((column, colIndex) => (
                      <div key={column.key || colIndex} className="flex justify-between items-start">
                        <span className="text-sm font-medium text-secondary-600 min-w-0 flex-1">
                          {column.header}:
                        </span>
                        <span className="text-sm text-secondary-900 ml-2 text-right">
                          {column.render ? column.render(row, rowIndex) : row[column.key]}
                        </span>
                      </div>
                    ))}
                </div>

                {/* Expandable content for additional columns */}
                {(expandable || columns.length > 3) && (
                  <>
                    {(isExpanded || expandable) && (
                      <div className="mt-4 pt-4 border-t border-secondary-200">
                        {expandable && renderExpandedContent ? (
                          renderExpandedContent(row, rowIndex)
                        ) : (
                          <div className="space-y-3">
                            {columns
                              .slice(3) // Show remaining columns when expanded
                              .map((column, colIndex) => (
                                <div key={column.key || colIndex} className="flex justify-between items-start">
                                  <span className="text-sm font-medium text-secondary-600 min-w-0 flex-1">
                                    {column.header}:
                                  </span>
                                  <span className="text-sm text-secondary-900 ml-2 text-right">
                                    {column.render ? column.render(row, rowIndex) : row[column.key]}
                                  </span>
                                </div>
                              ))}
                          </div>
                        )}
                      </div>
                    )}
                    
                    {(columns.length > 3 || expandable) && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleRowExpansion(rowId)
                        }}
                        className="mt-3 flex items-center text-sm text-primary-600 hover:text-primary-700"
                      >
                        {isExpanded ? (
                          <>
                            <ChevronDown className="h-4 w-4 mr-1" />
                            Show Less
                          </>
                        ) : (
                          <>
                            <ChevronRight className="h-4 w-4 mr-1" />
                            Show More
                          </>
                        )}
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default ResponsiveTable
