@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  --color-primary: #4f46e5;
  --color-navy: #0f172a;
  --color-cream: #fef9f3;
}

body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #fef9f3 0%, #f8fafc 100%);
  color: #334155;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(254, 249, 243, 0.5);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4f46e5, #6366f1);
  border-radius: 10px;
  border: 2px solid rgba(254, 249, 243, 0.5);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4338ca, #4f46e5);
}

/* Custom components */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 active:bg-primary-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium transform hover:-translate-y-0.5;
}

.btn-secondary {
  @apply bg-cream-100 hover:bg-cream-200 active:bg-cream-300 text-navy-800 font-semibold py-3 px-6 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium transform hover:-translate-y-0.5;
}

.btn-success {
  @apply bg-success-600 hover:bg-success-700 active:bg-success-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium transform hover:-translate-y-0.5;
}

.btn-warning {
  @apply bg-warning-500 hover:bg-warning-600 active:bg-warning-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium transform hover:-translate-y-0.5;
}

.btn-danger {
  @apply bg-danger-600 hover:bg-danger-700 active:bg-danger-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium transform hover:-translate-y-0.5;
}

.card {
  @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft border border-cream-200/50 p-8 transition-all duration-200 hover:shadow-medium;
}

.input-field {
  @apply w-full px-4 py-3 border border-secondary-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm;
}

.label {
  @apply block text-sm font-semibold text-navy-700 mb-3;
}
