import { useEffect, useState } from 'react'
import { supabase } from '../config/supabase'
import { AuthContext } from './AuthContextDefinition'

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Safety timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.warn('⚠️ Auth initialization timeout - forcing loading to false')
      setLoading(false)
    }, 10000) // 10 second timeout

    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('🔄 Getting initial session...')

        // Test Supabase connection first
        const { data: _testData, error: testError } = await supabase
          .from('users')
          .select('count')
          .limit(1)

        if (testError && testError.code !== 'PGRST116') {
          console.error('❌ Supabase connection test failed:', testError)
          // Continue anyway, might be a permissions issue
        } else {
          console.log('✅ Supabase connection test passed')
        }

        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('❌ Error getting session:', error)
          setLoading(false)
          return
        }

        if (session?.user) {
          console.log('✅ Found existing session for:', session.user.email)
          await fetchUserProfile(session.user.id)
        } else {
          console.log('ℹ️ No existing session found')
        }
      } catch (error) {
        console.error('❌ Error in getInitialSession:', error)
      } finally {
        clearTimeout(timeoutId)
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state change:', event, session?.user?.email)
        try {
          if (session?.user) {
            // Keep loading true while fetching profile
            setLoading(true)
            await fetchUserProfile(session.user.id)
          } else {
            console.log('❌ No session, clearing user')
            setUser(null)
          }
        } catch (error) {
          console.error('❌ Error in auth state change:', error)
          setUser(null)
        } finally {
          setLoading(false)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const fetchUserProfile = async (userId) => {
    try {
      console.log('👤 Fetching user profile for ID:', userId)
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('❌ Profile fetch error:', error)
        console.error('   Code:', error.code)
        console.error('   Message:', error.message)
        console.error('   Details:', error.details)

        // If it's a "not found" error, that's expected for new users
        if (error.code === 'PGRST116') {
          console.log('ℹ️ User profile not found - this is expected for new users')
          setUser(null)
          return
        }

        throw error
      }

      if (!data) {
        console.error('❌ No profile data returned')
        setUser(null)
        return
      }

      console.log('✅ User profile fetched:', data)
      setUser(data)
    } catch (error) {
      console.error('❌ Error fetching user profile:', error)
      setUser(null)
    }
  }

  // Public signup is disabled - only admins can create users
  const signUp = async () => {
    return {
      data: null,
      error: new Error('Public registration is disabled. Please contact an administrator to create your account.')
    }
  }

  const signIn = async (email, password) => {
    try {
      setLoading(true)
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      // Wait for the auth state change to complete and profile to be fetched
      // The auth state change listener will handle setting loading to false
      return { data, error: null }
    } catch (error) {
      setLoading(false)
      return { data: null, error }
    }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      setUser(null)
      return { error: null }
    } catch (error) {
      return { error }
    }
  }

  const updateProfile = async (updates) => {
    try {
      // Use our database function for profile updates
      const { error } = await supabase
        .rpc('update_user_profile', {
          user_id: user.id,
          first_name: updates.first_name,
          last_name: updates.last_name,
          phone: updates.phone
        })

      if (error) throw error

      // Refresh user profile
      await fetchUserProfile(user.id)
      return { data: true, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  // Helper function to check user role
  const hasRole = (role) => {
    return user?.role === role
  }

  // Helper function to check if user is admin
  const isAdmin = () => {
    return user?.role === 'admin'
  }

  // Helper function to check if user is investor
  const isInvestor = () => {
    return user?.role === 'investor'
  }

  // Helper function to check if user is borrower
  const isBorrower = () => {
    return user?.role === 'borrower'
  }

  const value = {
    user,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
    hasRole,
    isAdmin,
    isInvestor,
    isBorrower,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
