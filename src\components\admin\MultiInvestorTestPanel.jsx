import React, { useState, useMemo, useCallback } from 'react'
import {
  useLoans,
  useAvailableInvestors,
  useLoanFragmentationSuggestions,
  useExecuteLoanFragmentation,
  useLoanInvestmentTracking
} from '../../hooks/useApi'
import { useTestPanelReducer } from '../../hooks/useTestPanelReducer'
import { formatCurrency, formatPercentage } from '../../services'
import { LOAN_STATUS } from '../../config/supabase'

/**
 * Multi-Investor Test Panel Component
 * Comprehensive testing interface for the multi-investor loan funding system
 */
const MultiInvestorTestPanel = () => {
  // Use reducer for complex state management
  const { state, actions } = useTestPanelReducer()
  const { selectedLoanId, selectedStrategy, isExecuting, testResults } = state

  // Keep simple state as regular useState
  const [targetInvestorCount, setTargetInvestorCount] = useState(3)

  // Fetch data
  const { data: loans, isLoading: loansLoading } = useLoans({ status: LOAN_STATUS.APPROVED })
  const { data: investors, isLoading: investorsLoading } = useAvailableInvestors(1000)
  const {
    data: suggestions,
    refetch: refetchSuggestions
  } = useLoanFragmentationSuggestions(selectedLoanId, targetInvestorCount)
  const { 
    data: tracking, 
    refetch: refetchTracking 
  } = useLoanInvestmentTracking(selectedLoanId)

  const executeFragmentation = useExecuteLoanFragmentation()

  // Memoized calculations for expensive operations
  const totalInvestorBalance = useMemo(() => {
    if (!investors) return 0
    return investors.reduce((sum, inv) => sum + parseFloat(inv.wallet.available_balance), 0)
  }, [investors])

  const availableStrategies = useMemo(() => {
    return suggestions?.suggestions || []
  }, [suggestions])

  // Memoized test handlers using reducer actions
  const handleExecuteTest = useCallback(async () => {
    if (!selectedLoanId || !selectedStrategy) {
      alert('Please select a loan and strategy')
      return
    }

    actions.setExecuting(true)
    try {
      const allocations = selectedStrategy.allocations.map(alloc => ({
        investor_id: alloc.investor_id,
        amount: alloc.suggested_amount
      }))

      const result = await executeFragmentation.mutateAsync({
        loanId: selectedLoanId,
        allocations
      })

      actions.setTestResults(result)

      // Refresh data
      refetchSuggestions()
      refetchTracking()

      alert(`Test completed! ${result.successful_allocations} successful, ${result.failed_allocations} failed`)
    } catch (error) {
      alert(`Test failed: ${error.message}`)
      actions.setError(error.message)
    }
  }, [selectedLoanId, selectedStrategy, executeFragmentation, refetchSuggestions, refetchTracking, actions])

  const runComprehensiveTest = useCallback(async () => {
    actions.setExecuting(true)
    actions.resetTestResults()

    try {
      // Test 1: Check available investors
      console.log('Testing available investors...')
      if (!investors || investors.length === 0) {
        throw new Error('No investors available for testing')
      }

      // Test 2: Check approved loans
      console.log('Testing approved loans...')
      if (!loans || loans.length === 0) {
        throw new Error('No approved loans available for testing')
      }

      // Test 3: Generate fragmentation suggestions
      console.log('Testing fragmentation suggestions...')
      if (selectedLoanId && suggestions) {
        if (suggestions.suggestions.length === 0) {
          throw new Error('No fragmentation strategies generated')
        }
      }

      const testSummary = {
        available_investors: investors.length,
        approved_loans: loans.length,
        total_investor_balance: totalInvestorBalance,
        fragmentation_strategies: availableStrategies.length,
        test_status: 'PASSED',
        timestamp: new Date().toISOString()
      }

      actions.setTestResults(testSummary)
      alert('Comprehensive test completed successfully!')
    } catch (error) {
      const errorResult = {
        test_status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      }
      actions.setTestResults(errorResult)
      alert(`Comprehensive test failed: ${error.message}`)
    }
  }, [investors, loans, selectedLoanId, suggestions, totalInvestorBalance, availableStrategies, actions])

  return (
    <div className="bg-white rounded-lg shadow-md p-6 space-y-6">
      <div className="border-b pb-4">
        <h2 className="text-2xl font-bold text-gray-900">Multi-Investor System Test Panel</h2>
        <p className="text-gray-600 mt-2">
          Test the core multi-investor loan funding and fragmentation functionality.
        </p>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded p-4">
          <h3 className="font-semibold text-blue-900">Available Investors</h3>
          {investorsLoading ? (
            <div className="animate-pulse bg-blue-200 h-6 rounded mt-2"></div>
          ) : (
            <div>
              <p className="text-2xl font-bold text-blue-900">{investors?.length || 0}</p>
              <p className="text-sm text-blue-600">
                Total Balance: {formatCurrency(
                  investors?.reduce((sum, inv) => sum + parseFloat(inv.wallet.available_balance), 0) || 0
                )}
              </p>
            </div>
          )}
        </div>

        <div className="bg-green-50 border border-green-200 rounded p-4">
          <h3 className="font-semibold text-green-900">Approved Loans</h3>
          {loansLoading ? (
            <div className="animate-pulse bg-green-200 h-6 rounded mt-2"></div>
          ) : (
            <div>
              <p className="text-2xl font-bold text-green-900">{loans?.length || 0}</p>
              <p className="text-sm text-green-600">
                Total Value: {formatCurrency(
                  loans?.reduce((sum, loan) => sum + parseFloat(loan.amount), 0) || 0
                )}
              </p>
            </div>
          )}
        </div>

        <div className="bg-purple-50 border border-purple-200 rounded p-4">
          <h3 className="font-semibold text-purple-900">Fragmentation Strategies</h3>
          <div>
            <p className="text-2xl font-bold text-purple-900">{suggestions?.suggestions.length || 0}</p>
            <p className="text-sm text-purple-600">
              {selectedLoanId ? 'Generated for selected loan' : 'Select a loan to generate'}
            </p>
          </div>
        </div>
      </div>

      {/* Loan Selection */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-gray-800">Select Loan for Testing</h3>
        <div className="flex items-center space-x-4">
          <select
            value={selectedLoanId}
            onChange={(e) => actions.setSelectedLoan(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded"
          >
            <option value="">Select a loan...</option>
            {loans?.map((loan) => (
              <option key={loan.id} value={loan.id}>
                {loan.title} - {formatCurrency(loan.amount)} ({loan.status})
              </option>
            ))}
          </select>
          <input
            type="number"
            min="2"
            max="10"
            value={targetInvestorCount}
            onChange={(e) => setTargetInvestorCount(parseInt(e.target.value))}
            className="w-20 px-3 py-2 border border-gray-300 rounded"
            placeholder="Investors"
          />
          <button
            onClick={() => refetchSuggestions()}
            disabled={!selectedLoanId}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-300"
          >
            Generate Strategies
          </button>
        </div>
      </div>

      {/* Fragmentation Strategies */}
      {suggestions && suggestions.suggestions.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800">Fragmentation Strategies</h3>
          <div className="grid gap-3">
            {suggestions.suggestions.map((strategy, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedStrategy === strategy
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => actions.setSelectedStrategy(strategy)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-gray-900">{strategy.strategy}</h4>
                  <div className="text-sm text-gray-600">
                    {strategy.investor_count} investors • {formatCurrency(strategy.total_allocated)}
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-2">{strategy.description}</p>
                <div className="text-xs text-gray-500">
                  Allocations: {strategy.allocations.map(a => `${a.investor_name}: ${formatCurrency(a.suggested_amount)}`).join(', ')}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Investment Tracking */}
      {tracking && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800">Current Investment Tracking</h3>
          <div className="bg-gray-50 border border-gray-200 rounded p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Loan Amount</p>
                <p className="font-semibold">{formatCurrency(tracking.loan_details.amount)}</p>
              </div>
              <div>
                <p className="text-gray-600">Funded Amount</p>
                <p className="font-semibold">{formatCurrency(tracking.loan_details.funded_amount)}</p>
              </div>
              <div>
                <p className="text-gray-600">Funding Progress</p>
                <p className="font-semibold">{formatPercentage(tracking.loan_details.funding_percentage)}</p>
              </div>
              <div>
                <p className="text-gray-600">Investors</p>
                <p className="font-semibold">{tracking.funding_summary.total_investors}</p>
              </div>
            </div>
            
            {tracking.investment_breakdown.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-300">
                <h4 className="font-medium text-gray-900 mb-2">Current Investors</h4>
                <div className="space-y-2">
                  {tracking.investment_breakdown.map((investment) => (
                    <div key={investment.id} className="flex justify-between items-center text-sm">
                      <span>{investment.investor.first_name} {investment.investor.last_name}</span>
                      <div className="text-right">
                        <span className="font-medium">{formatCurrency(investment.amount)}</span>
                        <span className="text-gray-500 ml-2">({formatPercentage(investment.percentage)})</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Test Results */}
      {testResults && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800">Test Results</h3>
          <div className={`border rounded p-4 ${
            testResults.test_status === 'PASSED' || testResults.successful_allocations > 0
              ? 'bg-green-50 border-green-200'
              : 'bg-red-50 border-red-200'
          }`}>
            <pre className="text-sm overflow-x-auto">
              {JSON.stringify(testResults, null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          onClick={runComprehensiveTest}
          disabled={isExecuting}
          className={`px-4 py-2 rounded font-medium ${
            isExecuting
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}
        >
          {isExecuting ? 'Running...' : 'Run Comprehensive Test'}
        </button>
        <button
          onClick={handleExecuteTest}
          disabled={isExecuting || !selectedLoanId || !selectedStrategy}
          className={`px-6 py-2 rounded font-medium ${
            isExecuting || !selectedLoanId || !selectedStrategy
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isExecuting ? 'Executing...' : 'Execute Fragmentation Test'}
        </button>
      </div>
    </div>
  )
}

export default MultiInvestorTestPanel
