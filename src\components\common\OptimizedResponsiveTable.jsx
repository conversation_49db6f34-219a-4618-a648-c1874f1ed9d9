import React, { useState, memo, useCallback, useMemo } from 'react'
import { FixedSizeList as List } from 'react-window'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { useTablePerformanceMonitor } from '../../hooks/usePerformanceMonitor'

// Memoized Table Row Component for performance optimization
const TableRow = memo(({ 
  row, 
  rowIndex, 
  columns, 
  expandable, 
  expandedRows, 
  keyField, 
  onRowClick, 
  onToggleExpand,
  renderExpandedContent 
}) => {
  const rowId = row[keyField] || rowIndex
  const isExpanded = expandedRows.has(rowId)
  
  // Memoized row click handler
  const handleRowClick = useCallback(() => {
    if (onRowClick) {
      onRowClick(row, rowIndex)
    }
  }, [onRowClick, row, rowIndex])

  // Memoized expand toggle handler
  const handleToggleExpand = useCallback((e) => {
    e.stopPropagation()
    onToggleExpand(rowId)
  }, [onToggleExpand, rowId])

  // Memoized row class calculation
  const rowClassName = useMemo(() => {
    return `${onRowClick ? 'cursor-pointer hover:bg-secondary-50' : ''}`
  }, [onRowClick])

  return (
    <React.Fragment>
      <tr className={rowClassName} onClick={handleRowClick}>
        {expandable && (
          <td className="w-8 px-3 py-4">
            <button
              onClick={handleToggleExpand}
              className="text-secondary-400 hover:text-secondary-600"
              aria-label={isExpanded ? 'Collapse row' : 'Expand row'}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>
          </td>
        )}
        {columns.map((column, colIndex) => (
          <td
            key={column.key || colIndex}
            className={`px-6 py-4 whitespace-nowrap text-sm ${column.className || ''}`}
          >
            {column.render ? column.render(row, rowIndex) : row[column.key]}
          </td>
        ))}
      </tr>
      {expandable && isExpanded && renderExpandedContent && (
        <tr>
          <td colSpan={columns.length + 1} className="px-6 py-4 bg-secondary-50">
            {renderExpandedContent(row, rowIndex)}
          </td>
        </tr>
      )}
    </React.Fragment>
  )
}, (prevProps, nextProps) => {
  // Custom comparison for memo optimization
  const rowId = prevProps.row[prevProps.keyField] || prevProps.rowIndex
  const nextRowId = nextProps.row[nextProps.keyField] || nextProps.rowIndex
  
  return (
    prevProps.row === nextProps.row &&
    prevProps.columns === nextProps.columns &&
    prevProps.expandedRows.has(rowId) === nextProps.expandedRows.has(nextRowId) &&
    prevProps.expandable === nextProps.expandable
  )
})

// Virtualized Row Component for large datasets
const VirtualizedRow = memo(({ index, style, data }) => {
  const { 
    items, 
    columns, 
    expandable, 
    expandedRows, 
    keyField, 
    onRowClick, 
    onToggleExpand, 
    renderExpandedContent 
  } = data
  
  const row = items[index]
  
  return (
    <div style={style}>
      <TableRow
        row={row}
        rowIndex={index}
        columns={columns}
        expandable={expandable}
        expandedRows={expandedRows}
        keyField={keyField}
        onRowClick={onRowClick}
        onToggleExpand={onToggleExpand}
        renderExpandedContent={renderExpandedContent}
      />
    </div>
  )
})

// Memoized Mobile Card Component
const MobileCard = memo(({ 
  row, 
  rowIndex, 
  columns, 
  expandable, 
  expandedRows, 
  keyField, 
  onRowClick, 
  onToggleExpand,
  renderExpandedContent 
}) => {
  const rowId = row[keyField] || rowIndex
  const isExpanded = expandedRows.has(rowId)
  
  const handleCardClick = useCallback(() => {
    if (onRowClick) {
      onRowClick(row, rowIndex)
    }
  }, [onRowClick, row, rowIndex])

  const handleToggleExpand = useCallback((e) => {
    e.stopPropagation()
    onToggleExpand(rowId)
  }, [onToggleExpand, rowId])

  const cardClassName = useMemo(() => {
    return `bg-white p-4 rounded-lg shadow border border-secondary-200 ${
      onRowClick ? 'cursor-pointer hover:shadow-md' : ''
    }`
  }, [onRowClick])

  return (
    <div className={cardClassName} onClick={handleCardClick}>
      <div className="space-y-2">
        {columns.map((column, colIndex) => (
          <div key={column.key || colIndex} className="flex justify-between items-start">
            <span className="text-sm font-medium text-secondary-500 min-w-0 flex-1">
              {column.header}:
            </span>
            <span className="text-sm text-secondary-900 ml-2 text-right">
              {column.render ? column.render(row, rowIndex) : row[column.key]}
            </span>
          </div>
        ))}
        {expandable && (
          <div className="pt-2 border-t border-secondary-200">
            <button
              onClick={handleToggleExpand}
              className="flex items-center text-sm text-primary-600 hover:text-primary-700"
              aria-label={isExpanded ? 'Collapse details' : 'Expand details'}
            >
              {isExpanded ? (
                <>
                  <ChevronDown className="h-4 w-4 mr-1" />
                  Hide Details
                </>
              ) : (
                <>
                  <ChevronRight className="h-4 w-4 mr-1" />
                  Show Details
                </>
              )}
            </button>
            {isExpanded && renderExpandedContent && (
              <div className="mt-3 pt-3 border-t border-secondary-100">
                {renderExpandedContent(row, rowIndex)}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}, (prevProps, nextProps) => {
  const rowId = prevProps.row[prevProps.keyField] || prevProps.rowIndex
  const nextRowId = nextProps.row[nextProps.keyField] || nextProps.rowIndex
  
  return (
    prevProps.row === nextProps.row &&
    prevProps.columns === nextProps.columns &&
    prevProps.expandedRows.has(rowId) === nextProps.expandedRows.has(nextRowId) &&
    prevProps.expandable === nextProps.expandable
  )
})

/**
 * OptimizedResponsiveTable Component
 * High-performance responsive table with virtualization for large datasets
 * Supports expandable rows, custom rendering, and automatic mobile/desktop layouts
 */
const OptimizedResponsiveTable = ({
  data = [],
  columns = [],
  loading = false,
  emptyMessage = 'No data available',
  expandable = false,
  renderExpandedContent = null,
  onRowClick = null,
  keyField = 'id',
  className = '',
  virtualized = false,
  virtualHeight = 400,
  rowHeight = 60
}) => {
  const [expandedRows, setExpandedRows] = useState(new Set())

  // Performance monitoring for table rendering
  const { measureRowRender: _measureRowRender } = useTablePerformanceMonitor('OptimizedResponsiveTable', data.length, {
    threshold: 50 // Warn when rendering more than 50 rows
  })

  // Memoized toggle expand function
  const toggleExpand = useCallback((rowId) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev)
      if (newSet.has(rowId)) {
        newSet.delete(rowId)
      } else {
        newSet.add(rowId)
      }
      return newSet
    })
  }, [])

  // Memoized virtualization data
  const virtualData = useMemo(() => ({
    items: data,
    columns,
    expandable,
    expandedRows,
    keyField,
    onRowClick,
    onToggleExpand: toggleExpand,
    renderExpandedContent
  }), [data, columns, expandable, expandedRows, keyField, onRowClick, toggleExpand, renderExpandedContent])

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <div className="bg-secondary-50 h-12"></div>
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="bg-white h-16 border-b border-secondary-200"></div>
          ))}
        </div>
      </div>
    )
  }

  if (!data || data.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <p className="text-secondary-500">{emptyMessage}</p>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Desktop Table View */}
      <div className="hidden md:block overflow-x-auto shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        {virtualized && data.length > 50 ? (
          // Virtualized table for large datasets
          <div>
            <table className="min-w-full divide-y divide-secondary-300">
              <thead className="bg-secondary-50">
                <tr>
                  {expandable && <th className="w-8 px-3 py-3"></th>}
                  {columns.map((column, index) => (
                    <th
                      key={column.key || index}
                      className={`px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider ${column.headerClassName || ''}`}
                    >
                      {column.header}
                    </th>
                  ))}
                </tr>
              </thead>
            </table>
            <List
              height={virtualHeight}
              itemCount={data.length}
              itemSize={rowHeight}
              itemData={virtualData}
            >
              {VirtualizedRow}
            </List>
          </div>
        ) : (
          // Regular table for smaller datasets
          <table className="min-w-full divide-y divide-secondary-300">
            <thead className="bg-secondary-50">
              <tr>
                {expandable && <th className="w-8 px-3 py-3"></th>}
                {columns.map((column, index) => (
                  <th
                    key={column.key || index}
                    className={`px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider ${column.headerClassName || ''}`}
                  >
                    {column.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-secondary-200">
              {data.map((row, rowIndex) => (
                <TableRow
                  key={row[keyField] || rowIndex}
                  row={row}
                  rowIndex={rowIndex}
                  columns={columns}
                  expandable={expandable}
                  expandedRows={expandedRows}
                  keyField={keyField}
                  onRowClick={onRowClick}
                  onToggleExpand={toggleExpand}
                  renderExpandedContent={renderExpandedContent}
                />
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-4">
        {data.map((row, rowIndex) => (
          <MobileCard
            key={row[keyField] || rowIndex}
            row={row}
            rowIndex={rowIndex}
            columns={columns}
            expandable={expandable}
            expandedRows={expandedRows}
            keyField={keyField}
            onRowClick={onRowClick}
            onToggleExpand={toggleExpand}
            renderExpandedContent={renderExpandedContent}
          />
        ))}
      </div>
    </div>
  )
}

export default memo(OptimizedResponsiveTable)
