import React from 'react'
import { <PERSON><PERSON>hart, BarChart3, Target, TrendingUp } from 'lucide-react'
import { useAuth } from '../../../hooks/useAuth'
import { useInvestments } from '../../../hooks/useApi'

const DiversificationPage = () => {
  const { user } = useAuth()
  const { data: investments, isLoading } = useInvestments({ investor_id: user?.id })

  // Calculate diversification from real data
  const totalInvested = investments?.reduce((sum, inv) => sum + (inv.amount || 0), 0) || 0

  // Group by loan type
  const byLoanType = investments?.reduce((acc, inv) => {
    const type = inv.loan?.purpose || 'Other'
    const existing = acc.find(item => item.type === type)
    if (existing) {
      existing.amount += inv.amount || 0
    } else {
      acc.push({ type, amount: inv.amount || 0 })
    }
    return acc
  }, []) || []

  // Calculate percentages
  byLoanType.forEach(item => {
    item.percentage = totalInvested ? Math.round((item.amount / totalInvested) * 100) : 0
  })

  const diversificationData = {
    byLoanType,
    byRiskLevel: [], // Would need risk assessment data
    diversificationScore: byLoanType.length > 1 ? Math.min(byLoanType.length * 2, 10) : 0
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  const getDiversificationLevel = (score) => {
    if (score >= 8) return { level: 'Excellent', color: 'green' }
    if (score >= 6) return { level: 'Good', color: 'blue' }
    if (score >= 4) return { level: 'Fair', color: 'yellow' }
    return { level: 'Poor', color: 'red' }
  }

  const diversificationLevel = getDiversificationLevel(diversificationData.diversificationScore)

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Portfolio Diversification</h1>
        <p className="text-secondary-600 mt-2">
          Analyze your portfolio diversification across different loan types and risk levels
        </p>
      </div>

      {/* Diversification Score */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Diversification Score</h3>
        <div className="flex items-center mb-4">
          <div className={`bg-${diversificationLevel.color}-100 p-3 rounded-lg mr-4`}>
            <Target className={`h-8 w-8 text-${diversificationLevel.color}-600`} />
          </div>
          <div>
            <p className="text-2xl font-bold text-secondary-900">{diversificationLevel.level}</p>
            <p className="text-sm text-secondary-600">Score: {diversificationData.diversificationScore}/10</p>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`bg-${diversificationLevel.color}-600 h-2 rounded-full`}
            style={{ width: `${(diversificationData.diversificationScore / 10) * 100}%` }}
          ></div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Diversification by Loan Type */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <PieChart className="h-5 w-5 mr-2" />
            By Loan Type
          </h3>
          <div className="space-y-4">
            {diversificationData.byLoanType.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div 
                    className="w-4 h-4 rounded-full mr-3"
                    style={{ backgroundColor: `hsl(${index * 90}, 70%, 50%)` }}
                  ></div>
                  <span className="text-sm text-gray-700">{item.type}</span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    ${item.amount.toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500">{item.percentage}%</p>
                </div>
              </div>
            ))}
          </div>
          
          {/* Visual representation */}
          <div className="mt-6 h-4 bg-gray-200 rounded-full overflow-hidden flex">
            {diversificationData.byLoanType.map((item, index) => (
              <div
                key={index}
                className="h-full"
                style={{ 
                  width: `${item.percentage}%`,
                  backgroundColor: `hsl(${index * 90}, 70%, 50%)`
                }}
              ></div>
            ))}
          </div>
        </div>

        {/* Diversification by Risk Level */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            By Risk Level
          </h3>
          <div className="space-y-4">
            {diversificationData.byRiskLevel.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div 
                    className={`w-4 h-4 rounded-full mr-3 ${
                      item.level === 'Low Risk' ? 'bg-green-500' :
                      item.level === 'Medium Risk' ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                  ></div>
                  <span className="text-sm text-gray-700">{item.level}</span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    ${item.amount.toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500">{item.percentage}%</p>
                </div>
              </div>
            ))}
          </div>
          
          {/* Visual representation */}
          <div className="mt-6 h-4 bg-gray-200 rounded-full overflow-hidden flex">
            {diversificationData.byRiskLevel.map((item, index) => (
              <div
                key={index}
                className={`h-full ${
                  item.level === 'Low Risk' ? 'bg-green-500' :
                  item.level === 'Medium Risk' ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${item.percentage}%` }}
              ></div>
            ))}
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="mt-8 bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <TrendingUp className="h-5 w-5 mr-2" />
          Diversification Recommendations
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Improve Loan Type Diversity</h4>
            <p className="text-sm text-blue-700">
              Consider increasing allocation to education loans to improve diversification
            </p>
          </div>
          <div className="p-4 bg-green-50 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Risk Balance</h4>
            <p className="text-sm text-green-700">
              Your risk distribution is well balanced across different risk levels
            </p>
          </div>
          <div className="p-4 bg-yellow-50 rounded-lg">
            <h4 className="font-medium text-yellow-900 mb-2">Geographic Spread</h4>
            <p className="text-sm text-yellow-700">
              Consider diversifying across different geographic regions
            </p>
          </div>
          <div className="p-4 bg-purple-50 rounded-lg">
            <h4 className="font-medium text-purple-900 mb-2">Sector Allocation</h4>
            <p className="text-sm text-purple-700">
              Explore opportunities in emerging sectors for better diversification
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DiversificationPage
