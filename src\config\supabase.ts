import { createClient } from '@supabase/supabase-js'
import type { Database } from '../types/database'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// Re-export generated constants and types
export { Constants } from '../types/database'
export type {
  Database,
  User,
  Loan,
  Investment,
  Repayment,
  Wallet,
  WalletTransaction,
  InvestorDistribution,
  AuditLog,
  UserRole,
  LoanStatus,
  InvestmentStatus,
  RepaymentStatus,
  TransactionType,
  WalletTransactionStatus
} from '../types'

// Database table names (for backward compatibility)
export const TABLES = {
  USERS: 'users',
  LOANS: 'loans',
  INVESTMENTS: 'investments',
  REPAYMENTS: 'repayments',
  WALLETS: 'wallets',
  INVESTOR_DISTRIBUTIONS: 'investor_distributions',
  WALLET_TRANSACTIONS: 'wallet_transactions',
  AUDIT_LOGS: 'audit_logs',
} as const

// User roles (using generated constants)
export const USER_ROLES = {
  ADMIN: 'admin' as const,
  INVESTOR: 'investor' as const,
  BORROWER: 'borrower' as const,
} as const

// Loan statuses (using generated constants)
export const LOAN_STATUS = {
  DRAFT: 'draft' as const,
  PENDING: 'pending' as const,
  APPROVED: 'approved' as const,
  FUNDED: 'funded' as const,
  ACTIVE: 'active' as const,
  COMPLETED: 'completed' as const,
  DEFAULTED: 'defaulted' as const,
  CANCELLED: 'cancelled' as const,
} as const

// Investment statuses (using generated constants)
export const INVESTMENT_STATUS = {
  PENDING: 'pending' as const,
  ACTIVE: 'active' as const,
  COMPLETED: 'completed' as const,
  CANCELLED: 'cancelled' as const,
} as const

// Repayment statuses (using generated constants)
export const REPAYMENT_STATUS = {
  PENDING: 'pending' as const,
  COMPLETED: 'completed' as const,
  OVERDUE: 'overdue' as const,
  FAILED: 'failed' as const,
} as const

// Transaction types (using generated constants)
export const TRANSACTION_TYPE = {
  DEPOSIT: 'deposit' as const,
  WITHDRAWAL: 'withdrawal' as const,
  INVESTMENT: 'investment' as const,
  REPAYMENT: 'repayment' as const,
  FEE: 'fee' as const,
  INTEREST: 'interest' as const,
} as const

// Wallet transaction statuses (using generated constants)
export const WALLET_TRANSACTION_STATUS = {
  PENDING: 'pending' as const,
  COMPLETED: 'completed' as const,
  FAILED: 'failed' as const,
  CANCELLED: 'cancelled' as const,
} as const


