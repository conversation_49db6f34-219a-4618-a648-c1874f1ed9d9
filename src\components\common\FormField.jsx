import React from 'react'
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react'

/**
 * Standardized Form Field Component
 * Provides consistent styling and validation display across all forms
 */
const FormField = ({
  label,
  name,
  type = 'text',
  value,
  onChange,
  onBlur,
  error,
  touched,
  required = false,
  placeholder,
  disabled = false,
  className = '',
  helpText,
  showPasswordToggle = false,
  showPassword = false,
  onTogglePassword,
  ...props
}) => {
  const hasError = error && touched
  const isValid = !error && touched && value

  const handleChange = (e) => {
    onChange(name, e.target.value)
  }

  const handleBlur = () => {
    if (onBlur) {
      onBlur(name)
    }
  }

  return (
    <div className={`space-y-1 ${className}`}>
      {/* Label */}
      {label && (
        <label
          htmlFor={name}
          className="block text-sm font-semibold text-navy-700 mb-2 transition-colors duration-200"
        >
          {label}
          {required && <span className="text-danger-500 ml-1" aria-label="required">*</span>}
        </label>
      )}

      {/* Input Container */}
      <div className="relative">
        <input
          id={name}
          name={name}
          type={showPasswordToggle && !showPassword ? 'password' : type}
          value={value || ''}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          aria-invalid={hasError}
          aria-describedby={
            hasError ? `${name}-error` : 
            helpText ? `${name}-help` : undefined
          }
          className={`
            block w-full px-4 py-3 border rounded-lg shadow-soft
            placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-offset-0
            disabled:bg-secondary-50 disabled:text-secondary-500 disabled:cursor-not-allowed
            transition-all duration-300 ease-in-out
            text-navy-800 bg-white/80 backdrop-blur-sm
            hover:shadow-medium focus:shadow-medium
            ${hasError
              ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500/20'
              : isValid
                ? 'border-success-300 focus:border-success-500 focus:ring-success-500/20'
                : 'border-secondary-300 focus:border-primary-500 focus:ring-primary-500/20'
            }
            ${showPasswordToggle ? 'pr-12' : ''}
          `}
          {...props}
        />

        {/* Password Toggle Button */}
        {showPasswordToggle && (
          <button
            type="button"
            onClick={onTogglePassword}
            className="absolute inset-y-0 right-0 pr-4 flex items-center text-secondary-400 hover:text-navy-600 transition-colors duration-200"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <EyeOff className="h-5 w-5" />
            ) : (
              <Eye className="h-5 w-5" />
            )}
          </button>
        )}

        {/* Validation Icons */}
        {!showPasswordToggle && (hasError || isValid) && (
          <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
            {hasError ? (
              <AlertCircle className="h-5 w-5 text-danger-500 animate-pulse" aria-hidden="true" />
            ) : isValid ? (
              <CheckCircle className="h-5 w-5 text-success-500" aria-hidden="true" />
            ) : null}
          </div>
        )}
      </div>

      {/* Help Text */}
      {helpText && !hasError && (
        <p id={`${name}-help`} className="text-sm text-secondary-600 mt-1">
          {helpText}
        </p>
      )}

      {/* Error Message */}
      {hasError && (
        <p id={`${name}-error`} className="text-sm text-danger-600 flex items-center mt-1 animate-fadeIn">
          <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" aria-hidden="true" />
          {error}
        </p>
      )}
    </div>
  )
}

/**
 * Textarea Field Component
 */
export const TextareaField = ({
  label,
  name,
  value,
  onChange,
  onBlur,
  error,
  touched,
  required = false,
  placeholder,
  disabled = false,
  className = '',
  helpText,
  rows = 4,
  ...props
}) => {
  const hasError = error && touched
  const isValid = !error && touched && value

  const handleChange = (e) => {
    onChange(name, e.target.value)
  }

  const handleBlur = () => {
    if (onBlur) {
      onBlur(name)
    }
  }

  return (
    <div className={`space-y-1 ${className}`}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={name} 
          className="block text-sm font-medium text-secondary-700"
        >
          {label}
          {required && <span className="text-danger-500 ml-1" aria-label="required">*</span>}
        </label>
      )}

      {/* Textarea */}
      <div className="relative">
        <textarea
          id={name}
          name={name}
          value={value || ''}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          rows={rows}
          aria-invalid={hasError}
          aria-describedby={
            hasError ? `${name}-error` : 
            helpText ? `${name}-help` : undefined
          }
          className={`
            block w-full px-3 py-2 border rounded-md shadow-sm 
            placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-offset-0
            disabled:bg-secondary-50 disabled:text-secondary-500 disabled:cursor-not-allowed
            transition-colors duration-200 resize-vertical
            ${hasError 
              ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' 
              : isValid
                ? 'border-success-300 focus:border-success-500 focus:ring-success-500'
                : 'border-secondary-300 focus:border-primary-500 focus:ring-primary-500'
            }
          `}
          {...props}
        />

        {/* Validation Icons */}
        {(hasError || isValid) && (
          <div className="absolute top-2 right-2 pointer-events-none">
            {hasError ? (
              <AlertCircle className="h-4 w-4 text-danger-500" aria-hidden="true" />
            ) : isValid ? (
              <CheckCircle className="h-4 w-4 text-success-500" aria-hidden="true" />
            ) : null}
          </div>
        )}
      </div>

      {/* Help Text */}
      {helpText && !hasError && (
        <p id={`${name}-help`} className="text-sm text-secondary-600">
          {helpText}
        </p>
      )}

      {/* Error Message */}
      {hasError && (
        <p id={`${name}-error`} className="text-sm text-danger-600 flex items-center">
          <AlertCircle className="h-4 w-4 mr-1" aria-hidden="true" />
          {error}
        </p>
      )}
    </div>
  )
}

/**
 * Select Field Component
 */
export const SelectField = ({
  label,
  name,
  value,
  onChange,
  onBlur,
  error,
  touched,
  required = false,
  disabled = false,
  className = '',
  helpText,
  options = [],
  placeholder = 'Select an option...',
  ...props
}) => {
  const hasError = error && touched
  const isValid = !error && touched && value

  const handleChange = (e) => {
    onChange(name, e.target.value)
  }

  const handleBlur = () => {
    if (onBlur) {
      onBlur(name)
    }
  }

  return (
    <div className={`space-y-1 ${className}`}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={name} 
          className="block text-sm font-medium text-secondary-700"
        >
          {label}
          {required && <span className="text-danger-500 ml-1" aria-label="required">*</span>}
        </label>
      )}

      {/* Select */}
      <div className="relative">
        <select
          id={name}
          name={name}
          value={value || ''}
          onChange={handleChange}
          onBlur={handleBlur}
          disabled={disabled}
          required={required}
          aria-invalid={hasError}
          aria-describedby={
            hasError ? `${name}-error` : 
            helpText ? `${name}-help` : undefined
          }
          className={`
            block w-full px-3 py-2 border rounded-md shadow-sm 
            focus:outline-none focus:ring-2 focus:ring-offset-0
            disabled:bg-secondary-50 disabled:text-secondary-500 disabled:cursor-not-allowed
            transition-colors duration-200
            ${hasError 
              ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' 
              : isValid
                ? 'border-success-300 focus:border-success-500 focus:ring-success-500'
                : 'border-secondary-300 focus:border-primary-500 focus:ring-primary-500'
            }
          `}
          {...props}
        >
          <option value="" disabled>{placeholder}</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        {/* Validation Icons */}
        {(hasError || isValid) && (
          <div className="absolute inset-y-0 right-8 flex items-center pointer-events-none">
            {hasError ? (
              <AlertCircle className="h-4 w-4 text-danger-500" aria-hidden="true" />
            ) : isValid ? (
              <CheckCircle className="h-4 w-4 text-success-500" aria-hidden="true" />
            ) : null}
          </div>
        )}
      </div>

      {/* Help Text */}
      {helpText && !hasError && (
        <p id={`${name}-help`} className="text-sm text-secondary-600">
          {helpText}
        </p>
      )}

      {/* Error Message */}
      {hasError && (
        <p id={`${name}-error`} className="text-sm text-danger-600 flex items-center">
          <AlertCircle className="h-4 w-4 mr-1" aria-hidden="true" />
          {error}
        </p>
      )}
    </div>
  )
}

export default FormField
