import React, { useState } from 'react'
import { Download, FileText, Calendar, BarChart3 } from 'lucide-react'
import { useAuth } from '../../../hooks/useAuth'

const BorrowerReportsPage = () => {
  const [selectedReport, setSelectedReport] = useState('')
  const [dateRange, setDateRange] = useState('1y')
  const { user: _user } = useAuth()

  const reportTypes = [
    { 
      id: 'loan-summary', 
      name: 'Loan Summary Report', 
      description: 'Complete overview of all your loans and their current status' 
    },
    { 
      id: 'payment-history', 
      name: 'Payment History Report', 
      description: 'Detailed payment history and performance metrics' 
    },
    { 
      id: 'interest-analysis', 
      name: 'Interest Analysis Report', 
      description: 'Breakdown of interest payments and potential savings' 
    },
    { 
      id: 'credit-impact', 
      name: 'Credit Impact Report', 
      description: 'How your loan performance affects your credit profile' 
    },
    { 
      id: 'tax-summary', 
      name: 'Tax Summary Report', 
      description: 'Tax-related information for your business loans' 
    },
  ]

  const handleGenerateReport = () => {
    if (!selectedReport) {
      alert('Please select a report type')
      return
    }
    // Report generation logic will be implemented here
    console.log(`Generating ${selectedReport} report for ${dateRange}`)
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Borrower Reports</h1>
        <p className="text-secondary-600 mt-2">
          Generate comprehensive reports for your loan portfolio and payment history
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Report Configuration */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Generate Report</h3>
            
            {/* Report Type Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Select Report Type
              </label>
              <div className="space-y-3">
                {reportTypes.map((report) => (
                  <div key={report.id} className="flex items-start">
                    <input
                      type="radio"
                      id={report.id}
                      name="reportType"
                      value={report.id}
                      checked={selectedReport === report.id}
                      onChange={(e) => setSelectedReport(e.target.value)}
                      className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <div className="ml-3">
                      <label htmlFor={report.id} className="block text-sm font-medium text-gray-900 cursor-pointer">
                        {report.name}
                      </label>
                      <p className="text-sm text-gray-500">{report.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Date Range Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date Range
              </label>
              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="3m">Last 3 months</option>
                <option value="6m">Last 6 months</option>
                <option value="1y">Last year</option>
                <option value="2y">Last 2 years</option>
                <option value="all">All time</option>
                <option value="custom">Custom range</option>
              </select>
            </div>

            {/* Generate Button */}
            <button
              onClick={handleGenerateReport}
              className="w-full bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 flex items-center justify-center"
            >
              <Download className="h-4 w-4 mr-2" />
              Generate Report
            </button>
          </div>
        </div>

        {/* Recent Reports & Quick Stats */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Active Loans</span>
                <span className="text-sm font-medium text-gray-900">2</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Outstanding</span>
                <span className="text-sm font-medium text-gray-900">$43,500</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Payment Score</span>
                <span className="text-sm font-medium text-green-600">95/100</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Next Payment</span>
                <span className="text-sm font-medium text-gray-900">Aug 15</span>
              </div>
            </div>
          </div>

          {/* Recent Reports */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Reports</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                <div className="flex items-center">
                  <FileText className="h-4 w-4 text-gray-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Payment History</p>
                    <p className="text-xs text-gray-500">Generated 1 day ago</p>
                  </div>
                </div>
                <button className="text-primary-600 hover:text-primary-700">
                  <Download className="h-4 w-4" />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                <div className="flex items-center">
                  <FileText className="h-4 w-4 text-gray-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Loan Summary</p>
                    <p className="text-xs text-gray-500">Generated 3 days ago</p>
                  </div>
                </div>
                <button className="text-primary-600 hover:text-primary-700">
                  <Download className="h-4 w-4" />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                <div className="flex items-center">
                  <FileText className="h-4 w-4 text-gray-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Interest Analysis</p>
                    <p className="text-xs text-gray-500">Generated 1 week ago</p>
                  </div>
                </div>
                <button className="text-primary-600 hover:text-primary-700">
                  <Download className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Report Benefits */}
      <div className="mt-8 bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Why Generate Reports?</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <BarChart3 className="h-6 w-6 text-blue-600 mb-2" />
            <h4 className="font-medium text-blue-900 mb-2">Financial Planning</h4>
            <p className="text-sm text-blue-700">
              Use detailed reports for better financial planning and budgeting
            </p>
          </div>
          <div className="p-4 bg-green-50 rounded-lg">
            <FileText className="h-6 w-6 text-green-600 mb-2" />
            <h4 className="font-medium text-green-900 mb-2">Tax Preparation</h4>
            <p className="text-sm text-green-700">
              Generate tax-ready reports for business loan interest deductions
            </p>
          </div>
          <div className="p-4 bg-purple-50 rounded-lg">
            <Calendar className="h-6 w-6 text-purple-600 mb-2" />
            <h4 className="font-medium text-purple-900 mb-2">Credit Building</h4>
            <p className="text-sm text-purple-700">
              Track your payment history to build and maintain good credit
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BorrowerReportsPage
