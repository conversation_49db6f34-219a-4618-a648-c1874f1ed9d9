import React from 'react'
import { DollarSign, TrendingUp, <PERSON><PERSON><PERSON>, BarChart3 } from 'lucide-react'
import { useAuth } from '../../hooks/useAuth'
import { useInvestments } from '../../hooks/useApi'

const InvestorDashboard = () => {
  const { user } = useAuth()
  const { data: investments, isLoading } = useInvestments({ investor_id: user?.id })

  // Calculate stats from real data
  const stats = {
    totalInvested: investments?.reduce((sum, inv) => sum + (inv.amount || 0), 0) || 0,
    currentValue: investments?.reduce((sum, inv) => sum + (inv.current_value || inv.amount || 0), 0) || 0,
    totalReturns: investments?.reduce((sum, inv) => sum + (inv.returns || 0), 0) || 0,
    activeInvestments: investments?.filter(inv => inv.status === 'active').length || 0,
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Investor Dashboard</h1>
        <p className="text-secondary-600 mt-2">
          Investment overview and portfolio summary
        </p>
      </div>

      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="bg-blue-100 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Total Invested</p>
                <p className="text-2xl font-bold text-secondary-900">
                  ${stats.totalInvested.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="bg-green-100 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Current Value</p>
                <p className="text-2xl font-bold text-secondary-900">
                  ${stats.currentValue.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="bg-purple-100 p-3 rounded-lg">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Total Returns</p>
                <p className="text-2xl font-bold text-green-600">
                  +${stats.totalReturns.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="bg-orange-100 p-3 rounded-lg">
                <PieChart className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Active Investments</p>
                <p className="text-2xl font-bold text-secondary-900">{stats.activeInvestments}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Summary */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4">Portfolio Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">ROI</p>
              <p className="text-2xl font-bold text-green-600">
                {((stats.totalReturns / stats.totalInvested) * 100).toFixed(2)}%
              </p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Average Investment</p>
              <p className="text-2xl font-bold text-gray-900">
                ${(stats.totalInvested / stats.activeInvestments).toLocaleString()}
              </p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Monthly Returns</p>
              <p className="text-2xl font-bold text-green-600">
                +${(stats.totalReturns / 12).toFixed(0)}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InvestorDashboard
