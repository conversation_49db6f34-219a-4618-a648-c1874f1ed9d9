import React, { useState } from 'react'
import { useAuth } from '../../hooks/useAuth'
import { Settings as SettingsIcon, User, Bell, Shield, FileText } from 'lucide-react'

const BorrowerSettings = () => {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('profile')

  const tabs = [
    { id: 'profile', name: 'Profile', icon: User },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'loan', name: 'Loan Preferences', icon: FileText },
  ]

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Borrower Settings</h1>
        <p className="text-secondary-600 mt-2">
          Manage your account and loan preferences
        </p>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-secondary-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-secondary-900">Profile Information</h3>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-secondary-700">First Name</label>
                  <input
                    type="text"
                    defaultValue={user?.first_name || ''}
                    className="mt-1 block w-full border-secondary-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700">Last Name</label>
                  <input
                    type="text"
                    defaultValue={user?.last_name || ''}
                    className="mt-1 block w-full border-secondary-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700">Email</label>
                  <input
                    type="email"
                    defaultValue={user?.email || ''}
                    className="mt-1 block w-full border-secondary-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700">Phone</label>
                  <input
                    type="tel"
                    defaultValue={user?.phone || ''}
                    className="mt-1 block w-full border-secondary-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-secondary-900">Notification Preferences</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-secondary-900">Loan Updates</h4>
                    <p className="text-sm text-secondary-500">Get notified about your loan status</p>
                  </div>
                  <input type="checkbox" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-secondary-900">Payment Reminders</h4>
                    <p className="text-sm text-secondary-500">Receive reminders for upcoming payments</p>
                  </div>
                  <input type="checkbox" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-secondary-900">Application Updates</h4>
                    <p className="text-sm text-secondary-500">Get notified about application status changes</p>
                  </div>
                  <input type="checkbox" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-secondary-900">Security Settings</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700">Current Password</label>
                  <input
                    type="password"
                    className="mt-1 block w-full border-secondary-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700">New Password</label>
                  <input
                    type="password"
                    className="mt-1 block w-full border-secondary-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700">Confirm New Password</label>
                  <input
                    type="password"
                    className="mt-1 block w-full border-secondary-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'loan' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-secondary-900">Loan Preferences</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700">Preferred Loan Purpose</label>
                  <select className="mt-1 block w-full border-secondary-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    <option>Business Expansion</option>
                    <option>Equipment Purchase</option>
                    <option>Working Capital</option>
                    <option>Education</option>
                    <option>Personal</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700">Preferred Loan Term (months)</label>
                  <select className="mt-1 block w-full border-secondary-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    <option>6</option>
                    <option>12</option>
                    <option>18</option>
                    <option>24</option>
                    <option>36</option>
                  </select>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-secondary-900">Auto-apply for pre-approved loans</h4>
                    <p className="text-sm text-secondary-500">Automatically apply for loans you're pre-approved for</p>
                  </div>
                  <input type="checkbox" className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded" />
                </div>
              </div>
            </div>
          )}

          <div className="mt-6 flex justify-end">
            <button
              type="button"
              className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BorrowerSettings
