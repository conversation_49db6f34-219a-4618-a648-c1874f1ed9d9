import React, { useState, useMemo, useCallback } from 'react'
import {
  usePlatformAnalytics,
  useGeneratePlatformReport,
  useGenerateMonthlyFinancialReport
} from '../../hooks/useApi'
import { formatCurrency, formatPercentage } from '../../services'
import { TrendingUp, TrendingDown, Users, DollarSign, FileText, BarChart3, Download } from 'lucide-react'

/**
 * Advanced Analytics Dashboard Component
 * Comprehensive analytics and reporting interface for admin
 */
const AdvancedAnalyticsDashboard = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('all')
  const [activeTab, setActiveTab] = useState('overview')
  const [reportOptions, setReportOptions] = useState({
    includeUserAnalytics: true,
    includeLoanAnalytics: true,
    includeFinancialAnalytics: true
  })

  // Fetch analytics data
  const { data: analytics, isLoading: analyticsLoading, error: analyticsError } = usePlatformAnalytics(selectedTimeframe)

  // Report generation mutations
  const generatePlatformReport = useGeneratePlatformReport()
  const generateMonthlyReport = useGenerateMonthlyFinancialReport()

  // Memoized date calculation to prevent recreation on every render
  const getStartDate = useCallback((timeframe) => {
    const now = new Date()
    switch (timeframe) {
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      case '90d':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      case '1y':
        return new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      default:
        return '1900-01-01'
    }
  }, [])

  // Memoized report generation handlers
  const handleGeneratePlatformReport = useCallback(async () => {
    try {
      const report = await generatePlatformReport.mutateAsync({
        startDate: getStartDate(selectedTimeframe),
        endDate: new Date().toISOString().split('T')[0],
        ...reportOptions
      })

      // Download report as JSON (in a real app, you'd format this as PDF/Excel)
      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `platform-report-${new Date().toISOString().split('T')[0]}.json`
      a.click()
      URL.revokeObjectURL(url)

      alert('Platform report generated successfully!')
    } catch (error) {
      alert(`Failed to generate report: ${error.message}`)
    }
  }, [generatePlatformReport, getStartDate, selectedTimeframe, reportOptions])

  const handleGenerateMonthlyReport = useCallback(async () => {
    const now = new Date()
    try {
      const report = await generateMonthlyReport.mutateAsync({
        year: now.getFullYear(),
        month: now.getMonth() + 1
      })

      // Download report as JSON
      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `monthly-report-${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}.json`
      a.click()
      URL.revokeObjectURL(url)

      alert('Monthly financial report generated successfully!')
    } catch (error) {
      alert(`Failed to generate monthly report: ${error.message}`)
    }
  }, [generateMonthlyReport])

  // Memoized analytics calculations
  const _computedMetrics = useMemo(() => {
    if (!analytics) return null

    return {
      totalUsers: analytics.total_users || 0,
      totalLoans: analytics.total_loans || 0,
      totalInvestments: analytics.total_investments || 0,
      platformRevenue: analytics.platform_revenue || 0,
      averageROI: analytics.average_roi || 0,
      defaultRate: analytics.default_rate || 0
    }
  }, [analytics])

  if (analyticsLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (analyticsError) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <p className="text-red-600">Failed to load analytics data</p>
          <p className="text-gray-500 text-sm mt-2">{analyticsError.message}</p>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'financial', label: 'Financial', icon: DollarSign },
    { id: 'reports', label: 'Reports', icon: FileText }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Advanced Analytics Dashboard</h2>
            <p className="text-gray-600 mt-2">
              Comprehensive platform analytics and reporting
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="all">All Time</option>
              <option value="1y">Last Year</option>
              <option value="90d">Last 90 Days</option>
              <option value="30d">Last 30 Days</option>
            </select>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.overview.total_users}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">Growth this period</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Loan Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analytics.overview.total_loan_value)}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm text-gray-600">
                Funding Rate: {formatPercentage(analytics.overview.funding_rate)}
              </span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Platform ROI</p>
                <p className={`text-2xl font-bold ${
                  analytics.overview.platform_roi >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {formatPercentage(analytics.overview.platform_roi)}
                </p>
              </div>
              <div className={`p-3 rounded-full ${
                analytics.overview.platform_roi >= 0 ? 'bg-green-100' : 'bg-red-100'
              }`}>
                {analytics.overview.platform_roi >= 0 ? (
                  <TrendingUp className="h-6 w-6 text-green-600" />
                ) : (
                  <TrendingDown className="h-6 w-6 text-red-600" />
                )}
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm text-gray-600">
                Total Repaid: {formatCurrency(analytics.overview.total_repaid)}
              </span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Loans</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.overview.total_loans}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <FileText className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm text-gray-600">
                Total Investments: {analytics.overview.total_investments}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && analytics && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Platform Overview</h3>
              
              {/* Loan Status Distribution */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Loan Status Distribution</h4>
                  <div className="space-y-2">
                    {Object.entries(analytics.loan_status_distribution).map(([status, count]) => (
                      <div key={status} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span className="capitalize text-gray-700">{status}</span>
                        <span className="font-medium text-gray-900">{count} loans</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Top Performing Loans</h4>
                  <div className="space-y-2">
                    {analytics.top_performers.top_loans.slice(0, 5).map((loan) => (
                      <div key={loan.id} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <div>
                          <p className="font-medium text-gray-900">{loan.title}</p>
                          <p className="text-sm text-gray-600">{formatCurrency(loan.amount)}</p>
                        </div>
                        <div className="text-right">
                          <p className={`font-medium ${loan.roi >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatPercentage(loan.roi)}
                          </p>
                          <p className="text-xs text-gray-500">ROI</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Monthly Performance Chart */}
              {analytics.monthly_performance && analytics.monthly_performance.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Monthly Performance Trend</h4>
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="space-y-2">
                      {analytics.monthly_performance.slice(-6).map((month) => (
                        <div key={month.month} className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">{month.month}</span>
                          <span className="font-medium text-gray-900">{formatCurrency(month.amount)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Users Tab */}
          {activeTab === 'users' && analytics && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">User Analytics</h3>
              
              {/* User Growth Chart */}
              {analytics.user_growth && analytics.user_growth.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">User Growth Trend</h4>
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="space-y-2">
                      {analytics.user_growth.slice(-6).map((month) => (
                        <div key={month.month} className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">{month.month}</span>
                          <div className="flex space-x-4">
                            <span className="text-sm text-blue-600">Investors: {month.investors}</span>
                            <span className="text-sm text-green-600">Borrowers: {month.borrowers}</span>
                            <span className="font-medium text-gray-900">Total: {month.total}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Financial Tab */}
          {activeTab === 'financial' && analytics && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Financial Analytics</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900">Total Invested</h4>
                  <p className="text-2xl font-bold text-blue-900">
                    {formatCurrency(analytics.overview.total_invested)}
                  </p>
                  <p className="text-sm text-blue-600 mt-2">
                    Across {analytics.overview.total_investments} investments
                  </p>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-900">Total Repaid</h4>
                  <p className="text-2xl font-bold text-green-900">
                    {formatCurrency(analytics.overview.total_repaid)}
                  </p>
                  <p className="text-sm text-green-600 mt-2">
                    From {analytics.overview.total_repayments} repayments
                  </p>
                </div>

                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <h4 className="font-medium text-purple-900">Outstanding Balance</h4>
                  <p className="text-2xl font-bold text-purple-900">
                    {formatCurrency(analytics.overview.total_invested - analytics.overview.total_repaid)}
                  </p>
                  <p className="text-sm text-purple-600 mt-2">
                    Active loan balance
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Reports Tab */}
          {activeTab === 'reports' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Report Generation</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="border border-gray-200 rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">Platform Report</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Generate comprehensive platform analytics report including user growth, loan performance, and financial metrics.
                  </p>
                  
                  <div className="space-y-3 mb-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={reportOptions.includeUserAnalytics}
                        onChange={(e) => setReportOptions(prev => ({ ...prev, includeUserAnalytics: e.target.checked }))}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">Include User Analytics</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={reportOptions.includeLoanAnalytics}
                        onChange={(e) => setReportOptions(prev => ({ ...prev, includeLoanAnalytics: e.target.checked }))}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">Include Loan Analytics</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={reportOptions.includeFinancialAnalytics}
                        onChange={(e) => setReportOptions(prev => ({ ...prev, includeFinancialAnalytics: e.target.checked }))}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">Include Financial Analytics</span>
                    </label>
                  </div>
                  
                  <button
                    onClick={handleGeneratePlatformReport}
                    disabled={generatePlatformReport.isPending}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-300 flex items-center justify-center space-x-2"
                  >
                    <Download className="h-4 w-4" />
                    <span>{generatePlatformReport.isPending ? 'Generating...' : 'Generate Platform Report'}</span>
                  </button>
                </div>

                <div className="border border-gray-200 rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">Monthly Financial Report</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Generate detailed monthly financial report including cash flow, loan activity, and distribution analysis.
                  </p>
                  
                  <button
                    onClick={handleGenerateMonthlyReport}
                    disabled={generateMonthlyReport.isPending}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-300 flex items-center justify-center space-x-2"
                  >
                    <Download className="h-4 w-4" />
                    <span>{generateMonthlyReport.isPending ? 'Generating...' : 'Generate Monthly Report'}</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AdvancedAnalyticsDashboard
