import React, { createContext, useState, useCallback } from 'react'
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react'

// eslint-disable-next-line react-refresh/only-export-components
export const ToastContext = createContext()

const ToastItem = ({ toast, onRemove }) => {
  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-success-500" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-danger-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-warning-500" />
      case 'info':
        return <Info className="h-5 w-5 text-primary-500" />
      default:
        return <Info className="h-5 w-5 text-primary-500" />
    }
  }

  const getBackgroundColor = () => {
    switch (toast.type) {
      case 'success':
        return 'bg-success-50 border-success-200'
      case 'error':
        return 'bg-danger-50 border-danger-200'
      case 'warning':
        return 'bg-warning-50 border-warning-200'
      case 'info':
        return 'bg-primary-50 border-primary-200'
      default:
        return 'bg-secondary-50 border-secondary-200'
    }
  }

  const getTextColor = () => {
    switch (toast.type) {
      case 'success':
        return 'text-success-800'
      case 'error':
        return 'text-danger-800'
      case 'warning':
        return 'text-warning-800'
      case 'info':
        return 'text-primary-800'
      default:
        return 'text-secondary-800'
    }
  }

  return (
    <div
      className={`
        max-w-sm w-full shadow-lg rounded-lg pointer-events-auto border
        transform transition-all duration-300 ease-in-out
        ${getBackgroundColor()}
        ${toast.isExiting ? 'translate-x-full opacity-0' : 'translate-x-0 opacity-100'}
      `}
      role="alert"
      aria-live="polite"
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="ml-3 w-0 flex-1">
            {toast.title && (
              <p className={`text-sm font-medium ${getTextColor()}`}>
                {toast.title}
              </p>
            )}
            <p className={`text-sm ${toast.title ? 'mt-1' : ''} ${getTextColor()}`}>
              {toast.message}
            </p>
            {toast.action && (
              <div className="mt-3">
                <button
                  onClick={toast.action.onClick}
                  className={`
                    text-sm font-medium underline hover:no-underline focus:outline-none
                    ${toast.type === 'success' ? 'text-success-600 hover:text-success-500' :
                      toast.type === 'error' ? 'text-danger-600 hover:text-danger-500' :
                      toast.type === 'warning' ? 'text-warning-600 hover:text-warning-500' :
                      'text-primary-600 hover:text-primary-500'}
                  `}
                >
                  {toast.action.label}
                </button>
              </div>
            )}
          </div>
          <div className="ml-4 flex-shrink-0 flex">
            <button
              onClick={() => onRemove(toast.id)}
              className={`
                rounded-md inline-flex focus:outline-none focus:ring-2 focus:ring-offset-2
                ${toast.type === 'success' ? 'text-success-400 hover:text-success-500 focus:ring-success-500' :
                  toast.type === 'error' ? 'text-danger-400 hover:text-danger-500 focus:ring-danger-500' :
                  toast.type === 'warning' ? 'text-warning-400 hover:text-warning-500 focus:ring-warning-500' :
                  'text-primary-400 hover:text-primary-500 focus:ring-primary-500'}
              `}
              aria-label="Close notification"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

const ToastContainer = ({ toasts, onRemove }) => {
  return (
    <div
      className="fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50"
      aria-live="assertive"
    >
      <div className="w-full flex flex-col items-center space-y-4 sm:items-end">
        {toasts.map((toast) => (
          <ToastItem
            key={toast.id}
            toast={toast}
            onRemove={onRemove}
          />
        ))}
      </div>
    </div>
  )
}

export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([])

  const removeToast = useCallback((id) => {
    setToasts(prev =>
      prev.map(toast =>
        toast.id === id
          ? { ...toast, isExiting: true }
          : toast
      )
    )

    // Remove from DOM after animation
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id))
    }, 300)
  }, [])

  const addToast = useCallback((toast) => {
    const id = Date.now() + Math.random()
    const newToast = {
      id,
      type: 'info',
      duration: 5000,
      ...toast
    }

    setToasts(prev => [...prev, newToast])

    // Auto remove toast after duration
    if (newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id)
      }, newToast.duration)
    }

    return id
  }, [removeToast])



  const removeAllToasts = useCallback(() => {
    setToasts([])
  }, [])

  // Convenience methods
  const success = useCallback((message, options = {}) => {
    return addToast({
      type: 'success',
      message,
      ...options
    })
  }, [addToast])

  const error = useCallback((message, options = {}) => {
    return addToast({
      type: 'error',
      message,
      duration: 7000, // Longer duration for errors
      ...options
    })
  }, [addToast])

  const warning = useCallback((message, options = {}) => {
    return addToast({
      type: 'warning',
      message,
      ...options
    })
  }, [addToast])

  const info = useCallback((message, options = {}) => {
    return addToast({
      type: 'info',
      message,
      ...options
    })
  }, [addToast])

  const value = {
    toasts,
    addToast,
    removeToast,
    removeAllToasts,
    success,
    error,
    warning,
    info
  }

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </ToastContext.Provider>
  )
}

export default ToastProvider
