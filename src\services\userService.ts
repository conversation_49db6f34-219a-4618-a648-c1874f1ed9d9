import { supabase } from '../config/supabase'
import { TABLES, USER_ROLES } from '../config/supabase'
import type {
  User,
  UserInsert,
  UserUpdate,
  UserRole,
  ApiResponse
} from '../types'

/**
 * User Management Service
 * Handles all user-related API operations
 */

// Type definitions for service responses
interface UserFilters {
  role?: UserRole
  is_active?: boolean
  email_verified?: boolean
  search?: string
}

interface UserStatistics {
  total: number
  active: number
  verified: number
  byRole: {
    admin: number
    investor: number
    borrower: number
  }
}

// Get current user profile
export const getCurrentUser = async (): Promise<ApiResponse<User>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No authenticated user')

    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', user.id)
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Get user by ID (admin only or own profile)
export const getUserById = async (userId: string): Promise<ApiResponse<User>> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', userId)
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Get all users (admin only)
export const getAllUsers = async (filters: UserFilters = {}): Promise<ApiResponse<User[]>> => {
  try {
    let query = supabase
      .from(TABLES.USERS)
      .select('*')
      .order('created_at', { ascending: false })

    // Apply filters
    if (filters.role) {
      query = query.eq('role', filters.role)
    }
    if (filters.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active)
    }
    if (filters.email_verified !== undefined) {
      query = query.eq('email_verified', filters.email_verified)
    }
    if (filters.search) {
      query = query.or(`first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`)
    }

    const { data, error } = await query

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Get users by role
export const getUsersByRole = async (role: UserRole): Promise<ApiResponse<User[]>> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('role', role)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Update user profile
export const updateUserProfile = async (userId: string, updates: Partial<UserUpdate>): Promise<ApiResponse<void>> => {
  try {
    const { error } = await supabase
      .rpc('update_user_profile', {
        user_id: userId,
        first_name: updates.first_name,
        last_name: updates.last_name,
        phone: updates.phone
      })

    if (error) throw error
    
    // Return updated user data
    return await getUserById(userId)
  } catch (error) {
    return { data: null, error }
  }
}

// Update user role (admin only)
export const updateUserRole = async (userId: string, newRole: UserRole): Promise<ApiResponse<User>> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .update({ role: newRole })
      .eq('id', userId)
      .select()
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Activate/Deactivate user (admin only)
export const updateUserStatus = async (userId: string, isActive: boolean): Promise<ApiResponse<User>> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .update({ is_active: isActive })
      .eq('id', userId)
      .select()
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Verify user email (admin only)
export const verifyUserEmail = async (userId: string): Promise<ApiResponse<User>> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .update({ email_verified: true })
      .eq('id', userId)
      .select()
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Get user statistics (admin only)
export const getUserStatistics = async (): Promise<ApiResponse<UserStatistics>> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('role, is_active, email_verified')

    if (error) throw error

    const stats = {
      total: data.length,
      active: data.filter(u => u.is_active).length,
      verified: data.filter(u => u.email_verified).length,
      byRole: {
        admin: data.filter(u => u.role === USER_ROLES.ADMIN).length,
        investor: data.filter(u => u.role === USER_ROLES.INVESTOR).length,
        borrower: data.filter(u => u.role === USER_ROLES.BORROWER).length,
      }
    }

    return { data: stats, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Delete user (admin only - soft delete by deactivating)
export const deleteUser = async (userId: string): Promise<ApiResponse<User>> => {
  try {
    // Soft delete by deactivating the user
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .update({ is_active: false })
      .eq('id', userId)
      .select()
      .single()

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Search users (admin only)
export const searchUsers = async (searchTerm: string, filters: UserFilters = {}): Promise<ApiResponse<User[]>> => {
  try {
    let query = supabase
      .from(TABLES.USERS)
      .select('*')
      .or(`first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)

    if (filters.role) {
      query = query.eq('role', filters.role)
    }
    if (filters.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active)
    }

    query = query.order('created_at', { ascending: false })

    const { data, error } = await query

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Create new user (admin only)
export const createUser = async (userData: UserInsert & { password: string }): Promise<ApiResponse<User>> => {
  try {
    // This function requires the service role key and should only be called from admin interface
    // For security, this would typically be done through a secure API endpoint
    // For now, we'll use the RPC function approach similar to the seed script

    const { data, error } = await supabase
      .rpc('admin_create_user', {
        user_email: userData.email,
        user_password: userData.password,
        first_name: userData.first_name,
        last_name: userData.last_name,
        user_role: userData.role,
        phone: userData.phone || null
      })

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}
