import React, { useState, useCallback } from 'react'
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff,
  AlertCircle,
  CheckCircle,
  UserPlus
} from 'lucide-react'
import {
  getAllUsers,
  createUser,
  updateUserRole,
  updateUserStatus,
  verifyUserEmail,
  getUserStatistics
} from '../../services/userService'
import { USER_ROLES } from '../../config/supabase'
import FormField, { SelectField } from '../common/FormField'
import { useFormValidation, commonSchemas, validationRules } from '../../utils/validation'
import { useToast } from '../../hooks/useToast'
import { ButtonLoading, TableLoading, SectionLoading } from '../common/LoadingStates'
import ResponsiveTable from '../common/ResponsiveTable'
import { useConfirmation } from '../../hooks/useConfirmation.jsx'

/**
 * User Management Component
 * Comprehensive admin interface for managing users
 */
const UserManagement = () => {
  const toast = useToast()
  const { showConfirmation, ConfirmationComponent } = useConfirmation()
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [success, setSuccess] = useState('')
  const [error, setError] = useState('')
  const [filters, setFilters] = useState({
    role: '',
    is_active: '',
    search: ''
  })
  const [stats, setStats] = useState(null)

  // Create user form state with validation
  const initialFormData = {
    email: '',
    password: '',
    confirmPassword: '',
    first_name: '',
    last_name: '',
    role: 'investor',
    phone: ''
  }

  // Enhanced validation schema with confirm password validation
  const userValidationSchema = {
    ...commonSchemas.userCreation,
    confirmPassword: {
      label: 'Confirm Password',
      required: true,
      rules: [
        validationRules.required,
        validationRules.confirmPassword(() => formData.password)
      ]
    }
  }

  const {
    formData,
    errors,
    touched,
    handleChange,
    handleBlur,
    validateAll,
    resetForm
  } = useFormValidation(initialFormData, userValidationSchema)

  const [showPassword, setShowPassword] = useState(false)
  const [createLoading, setCreateLoading] = useState(false)

  // Load users and statistics
  const loadUsers = useCallback(async () => {
    setLoading(true)
    try {
      const [usersResult, statsResult] = await Promise.all([
        getAllUsers(filters),
        getUserStatistics()
      ])

      if (usersResult.error) throw usersResult.error
      if (statsResult.error) throw statsResult.error

      setUsers(usersResult.data || [])
      setStats(statsResult.data)
      setError('')
    } catch (err) {
      setError(`Failed to load users: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // Handle form submission
  const handleCreateUser = async (e) => {
    e.preventDefault()

    if (!validateAll()) {
      toast.error('Please fix the validation errors before submitting')
      return
    }

    setCreateLoading(true)

    try {
      const userData = {
        email: formData.email,
        password: formData.password,
        first_name: formData.first_name,
        last_name: formData.last_name,
        role: formData.role,
        phone: formData.phone || null
      }

      const result = await createUser(userData)

      if (result.error) {
        throw new Error(result.error.message || 'Failed to create user')
      }

      toast.success(`User ${formData.first_name} ${formData.last_name} created successfully`, {
        title: 'User Created'
      })
      setShowCreateForm(false)
      resetForm()
      loadUsers()
    } catch (err) {
      toast.error(err.message, {
        title: 'Failed to Create User'
      })
    } finally {
      setCreateLoading(false)
    }
  }



  // Handle user role update
  const handleUpdateRole = async (userId, newRole) => {
    try {
      const { error } = await updateUserRole(userId, newRole)
      if (error) throw error
      toast.success('User role updated successfully!', {
        title: 'Role Updated'
      })
      loadUsers()
    } catch (err) {
      toast.error(`Failed to update user role: ${err.message}`, {
        title: 'Update Failed'
      })
    }
  }

  // Handle user status toggle
  const handleToggleStatus = (userId, currentStatus, userName) => {
    const action = currentStatus ? 'deactivate' : 'activate'
    const actionPast = currentStatus ? 'deactivated' : 'activated'

    showConfirmation({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} User`,
      message: `Are you sure you want to ${action} ${userName}? ${
        currentStatus
          ? 'This will prevent them from accessing the platform.'
          : 'This will restore their access to the platform.'
      }`,
      type: currentStatus ? 'deactivate' : 'info',
      confirmText: action.charAt(0).toUpperCase() + action.slice(1),
      onConfirm: async () => {
        try {
          const { error } = await updateUserStatus(userId, !currentStatus)
          if (error) throw error
          toast.success(`User ${actionPast} successfully!`, {
            title: 'Status Updated'
          })
          loadUsers()
        } catch (err) {
          toast.error(`Failed to update user status: ${err.message}`, {
            title: 'Update Failed'
          })
          throw err // Re-throw to keep dialog open on error
        }
      }
    })
  }

  // Handle email verification
  const handleVerifyEmail = async (userId) => {
    try {
      const { error } = await verifyUserEmail(userId)
      if (error) throw error
      toast.success('User email verified successfully!', {
        title: 'Email Verified'
      })
      loadUsers()
    } catch (err) {
      toast.error(`Failed to verify email: ${err.message}`, {
        title: 'Verification Failed'
      })
    }
  }

  // Define table columns for ResponsiveTable
  const tableColumns = [
    {
      key: 'user',
      header: 'User',
      render: (user) => (
        <div>
          <div className="text-sm font-medium text-secondary-900">
            {user.first_name} {user.last_name}
          </div>
          <div className="text-sm text-secondary-500">{user.email}</div>
        </div>
      )
    },
    {
      key: 'role',
      header: 'Role',
      render: (user) => (
        <select
          value={user.role}
          onChange={(e) => handleUpdateRole(user.id, e.target.value)}
          className="text-sm border-secondary-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
          onClick={(e) => e.stopPropagation()}
        >
          <option value="investor">Investor</option>
          <option value="borrower">Borrower</option>
          <option value="admin">Admin</option>
        </select>
      )
    },
    {
      key: 'status',
      header: 'Status',
      render: (user) => (
        <div className="flex flex-col space-y-1">
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            user.is_active
              ? 'bg-success-100 text-success-800'
              : 'bg-danger-100 text-danger-800'
          }`}>
            {user.is_active ? 'Active' : 'Inactive'}
          </span>
          {!user.email_verified && (
            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-warning-100 text-warning-800">
              Unverified
            </span>
          )}
        </div>
      )
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (user) => (
        <span className="text-sm text-secondary-900">
          {new Date(user.created_at).toLocaleDateString()}
        </span>
      ),
      showInMobile: false // Hide in mobile view
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (user) => (
        <div className="flex space-x-2">
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleToggleStatus(user.id, user.is_active, `${user.first_name} ${user.last_name}`)
            }}
            className={`px-3 py-1 text-xs font-medium rounded-md ${
              user.is_active
                ? 'bg-danger-100 text-danger-700 hover:bg-danger-200'
                : 'bg-success-100 text-success-700 hover:bg-success-200'
            }`}
          >
            {user.is_active ? 'Deactivate' : 'Activate'}
          </button>
          {!user.email_verified && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                handleVerifyEmail(user.id)
              }}
              className="px-3 py-1 text-xs font-medium rounded-md bg-primary-100 text-primary-700 hover:bg-primary-200"
            >
              Verify Email
            </button>
          )}
        </div>
      )
    }
  ]

  // Load users on component mount
  React.useEffect(() => {
    loadUsers()
  }, [loadUsers])

  // Clear messages after 5 seconds
  React.useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess('')
        setError('')
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [success, error])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
          <p className="text-gray-600">Manage platform users and their roles</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
        >
          <UserPlus className="h-4 w-4 mr-2" />
          Create User
        </button>
      </div>



      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-gray-900">{stats.active}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Investors</p>
                <p className="text-2xl font-bold text-gray-900">{stats.byRole.investor}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-orange-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Borrowers</p>
                <p className="text-2xl font-bold text-gray-900">{stats.byRole.borrower}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search users..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
            <select
              value={filters.role}
              onChange={(e) => handleFilterChange('role', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Roles</option>
              <option value="admin">Admin</option>
              <option value="investor">Investor</option>
              <option value="borrower">Borrower</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filters.is_active}
              onChange={(e) => handleFilterChange('is_active', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={loadUsers}
              disabled={loading}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:bg-gray-300"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </div>
      </div>

      {/* Create User Form Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Create New User</h3>
              <button
                onClick={() => setShowCreateForm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            <form onSubmit={handleCreateUser} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  label="First Name"
                  name="first_name"
                  type="text"
                  value={formData.first_name}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={errors.first_name}
                  touched={touched.first_name}
                  required
                  placeholder="Enter first name"
                />
                <FormField
                  label="Last Name"
                  name="last_name"
                  type="text"
                  value={formData.last_name}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={errors.last_name}
                  touched={touched.last_name}
                  required
                  placeholder="Enter last name"
                />
              </div>

              <FormField
                label="Email Address"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.email}
                touched={touched.email}
                required
                placeholder="Enter email address"
              />

              <FormField
                label="Phone Number"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.phone}
                touched={touched.phone}
                placeholder="Enter phone number (optional)"
                helpText="Optional: Include country code for international numbers"
              />

              <SelectField
                label="Role"
                name="role"
                value={formData.role}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.role}
                touched={touched.role}
                required
                options={[
                  { value: 'investor', label: 'Investor' },
                  { value: 'borrower', label: 'Borrower' },
                  { value: 'admin', label: 'Admin' }
                ]}
                placeholder="Select user role"
              />

              <FormField
                label="Password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.password}
                touched={touched.password}
                required
                placeholder="Enter secure password"
                showPasswordToggle
                showPassword={showPassword}
                onTogglePassword={() => setShowPassword(!showPassword)}
                helpText="Must be at least 8 characters with uppercase, lowercase, number, and special character"
              />

              <FormField
                label="Confirm Password"
                name="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.confirmPassword}
                touched={touched.confirmPassword}
                required
                placeholder="Confirm your password"
              />

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <ButtonLoading
                  type="submit"
                  loading={createLoading}
                  loadingText="Creating User..."
                  variant="primary"
                >
                  Create User
                </ButtonLoading>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Users Table */}
      <ResponsiveTable
        data={users}
        columns={tableColumns}
        loading={loading}
        emptyMessage="No users found"
        className="bg-white rounded-lg shadow-sm border overflow-hidden"
        keyField="id"
      />

      {/* Confirmation Dialog */}
      <ConfirmationComponent />
    </div>
  )
}

export default UserManagement
