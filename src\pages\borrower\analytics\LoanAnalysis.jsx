import React, { useState } from 'react'
import { FileText, DollarSign, Calendar, TrendingUp } from 'lucide-react'
import { useAuth } from '../../../hooks/useAuth'
import { useLoans } from '../../../hooks/useApi'

const LoanAnalysisPage = () => {
  const [timeframe, setTimeframe] = useState('all')
  const { user } = useAuth()
  const { data: loans, isLoading } = useLoans({ borrower_id: user?.id })

  // Calculate analytics from real data
  const loanData = {
    totalLoans: loans?.length || 0,
    totalBorrowed: loans?.reduce((sum, loan) => sum + (loan.amount || 0), 0) || 0,
    averageLoanSize: loans?.length ? (loans.reduce((sum, loan) => sum + (loan.amount || 0), 0) / loans.length) : 0,
    averageInterestRate: loans?.length ? (loans.reduce((sum, loan) => sum + (loan.interest_rate || 0), 0) / loans.length) : 0,
    loans: loans || []
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'blue'
      case 'Completed': return 'green'
      case 'Pending': return 'yellow'
      case 'Overdue': return 'red'
      default: return 'gray'
    }
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Loan Analysis</h1>
        <p className="text-secondary-600 mt-2">
          Detailed analysis of your loan portfolio and borrowing patterns
        </p>
      </div>

      {/* Timeframe Selector */}
      <div className="mb-6">
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          <option value="all">All Time</option>
          <option value="1y">Last Year</option>
          <option value="6m">Last 6 Months</option>
          <option value="3m">Last 3 Months</option>
        </select>
      </div>

      {/* Loan Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Total Loans</p>
              <p className="text-2xl font-bold text-secondary-900">{loanData.totalLoans}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Total Borrowed</p>
              <p className="text-2xl font-bold text-secondary-900">
                ${loanData.totalBorrowed.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-lg">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Avg Loan Size</p>
              <p className="text-2xl font-bold text-secondary-900">
                ${loanData.averageLoanSize.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-orange-100 p-3 rounded-lg">
              <Calendar className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Avg Interest Rate</p>
              <p className="text-2xl font-bold text-secondary-900">{loanData.averageInterestRate}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Loan Details Table */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Loan Portfolio Details</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Loan ID</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Amount</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Purpose</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Interest Rate</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Term</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Remaining</th>
              </tr>
            </thead>
            <tbody>
              {loanData.loans.map((loan) => (
                <tr key={loan.id} className="border-b border-gray-100">
                  <td className="py-3 px-4 text-sm text-gray-900">#{loan.id}</td>
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">
                    ${loan.amount.toLocaleString()}
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-900">{loan.purpose}</td>
                  <td className="py-3 px-4 text-sm text-gray-900">{loan.interestRate}%</td>
                  <td className="py-3 px-4 text-sm text-gray-900">{loan.term} months</td>
                  <td className="py-3 px-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-${getStatusColor(loan.status)}-100 text-${getStatusColor(loan.status)}-800`}>
                      {loan.status}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-900">
                    ${loan.remainingBalance.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Loan Performance Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Borrowing Patterns</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm text-gray-600">Most Common Purpose</span>
              <span className="text-sm font-medium text-gray-900">Business Expansion</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm text-gray-600">Preferred Loan Term</span>
              <span className="text-sm font-medium text-gray-900">12-24 months</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm text-gray-600">Credit Utilization</span>
              <span className="text-sm font-medium text-gray-900">65%</span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="text-sm text-gray-600">Payment History</span>
              <span className="text-sm font-medium text-green-600">Excellent</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recommendations</h3>
          <div className="space-y-3">
            <div className="p-3 bg-green-50 rounded-lg">
              <p className="text-sm font-medium text-green-900">Credit Score</p>
              <p className="text-xs text-green-700">Your excellent payment history qualifies you for better rates</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm font-medium text-blue-900">Loan Consolidation</p>
              <p className="text-xs text-blue-700">Consider consolidating smaller loans for better terms</p>
            </div>
            <div className="p-3 bg-yellow-50 rounded-lg">
              <p className="text-sm font-medium text-yellow-900">Future Planning</p>
              <p className="text-xs text-yellow-700">Plan ahead for seasonal business needs to secure better rates</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoanAnalysisPage
