import React from 'react'

const Logo = ({ 
  size = 'md', 
  variant = 'full', 
  className = '',
  showText = true 
}) => {
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-12',
    lg: 'h-16',
    xl: 'h-20'
  }

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-3xl',
    xl: 'text-4xl'
  }

  if (variant === 'icon') {
    return (
      <div className={`flex items-center ${className}`}>
        <svg 
          className={`${sizeClasses[size]} w-auto`}
          viewBox="0 0 48 48" 
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Outer circle with gradient */}
          <circle 
            cx="24" 
            cy="24" 
            r="22" 
            fill="url(#gradient1)"
            stroke="url(#gradient2)" 
            strokeWidth="2"
          />
          
          {/* Inner geometric pattern */}
          <path 
            d="M16 20L24 12L32 20L28 24L24 20L20 24L16 20Z" 
            fill="white" 
            fillOpacity="0.9"
          />
          <path 
            d="M16 28L24 36L32 28L28 24L24 28L20 24L16 28Z" 
            fill="white" 
            fillOpacity="0.7"
          />
          
          {/* Central diamond */}
          <circle 
            cx="24" 
            cy="24" 
            r="3" 
            fill="white"
          />
          
          <defs>
            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#4f46e5" />
              <stop offset="50%" stopColor="#6366f1" />
              <stop offset="100%" stopColor="#312e81" />
            </linearGradient>
            <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#fef9f3" />
              <stop offset="100%" stopColor="#f0c087" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    )
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Logo Icon */}
      <svg 
        className={`${sizeClasses[size]} w-auto`}
        viewBox="0 0 48 48" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Outer circle with gradient */}
        <circle 
          cx="24" 
          cy="24" 
          r="22" 
          fill="url(#gradient1)"
          stroke="url(#gradient2)" 
          strokeWidth="2"
        />
        
        {/* Inner geometric pattern representing connection/network */}
        <path 
          d="M16 20L24 12L32 20L28 24L24 20L20 24L16 20Z" 
          fill="white" 
          fillOpacity="0.9"
        />
        <path 
          d="M16 28L24 36L32 28L28 24L24 28L20 24L16 28Z" 
          fill="white" 
          fillOpacity="0.7"
        />
        
        {/* Central diamond representing the platform core */}
        <circle 
          cx="24" 
          cy="24" 
          r="3" 
          fill="white"
        />
        
        <defs>
          <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#4f46e5" />
            <stop offset="50%" stopColor="#6366f1" />
            <stop offset="100%" stopColor="#312e81" />
          </linearGradient>
          <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#fef9f3" />
            <stop offset="100%" stopColor="#f0c087" />
          </linearGradient>
        </defs>
      </svg>
      
      {/* Logo Text */}
      {showText && (
        <div className="flex flex-col">
          <span className={`font-bold ${textSizeClasses[size]} bg-gradient-to-r from-navy-900 to-primary-700 bg-clip-text text-transparent leading-none`}>
            Ontare
          </span>
          {size !== 'sm' && (
            <span className="text-xs font-medium text-secondary-600 tracking-wide uppercase">
              Loan Platform
            </span>
          )}
        </div>
      )}
    </div>
  )
}

export default Logo
