import React, { useState } from 'react'
import { CreditCard, Calendar, TrendingUp, CheckCircle } from 'lucide-react'
import { useAuth } from '../../../hooks/useAuth'
import { useRepayments, useLoans } from '../../../hooks/useApi'

const RepaymentAnalysisPage = () => {
  const [timeframe, setTimeframe] = useState('6m')
  const { user } = useAuth()
  const { data: repayments, isLoading: repaymentsLoading } = useRepayments({ borrower_id: user?.id })
  const { data: loans, isLoading: loansLoading } = useLoans({ borrower_id: user?.id })

  const isLoading = repaymentsLoading || loansLoading

  // Calculate repayment analytics from real data
  const repaymentData = {
    totalPaid: repayments?.reduce((sum, payment) => sum + (payment.amount || 0), 0) || 0,
    onTimePayments: repayments?.filter(payment => payment.status === 'on_time').length || 0,
    latePayments: repayments?.filter(payment => payment.status === 'late').length || 0,
    paymentScore: repayments?.length ? Math.round((repayments.filter(p => p.status === 'on_time').length / repayments.length) * 100) : 0,
    nextPaymentDue: loans?.find(loan => loan.status === 'active')?.next_payment_date || null,
    nextPaymentAmount: loans?.find(loan => loan.status === 'active')?.monthly_payment || 0,
    monthlyPayments: repayments?.slice(0, 6) || []
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  const getPaymentScore = (score) => {
    if (score >= 90) return { level: 'Excellent', color: 'green' }
    if (score >= 80) return { level: 'Good', color: 'blue' }
    if (score >= 70) return { level: 'Fair', color: 'yellow' }
    return { level: 'Poor', color: 'red' }
  }

  const paymentLevel = getPaymentScore(repaymentData.paymentScore)

  const getStatusColor = (status) => {
    switch (status) {
      case 'On Time': return 'green'
      case 'Late': return 'red'
      case 'Early': return 'blue'
      default: return 'gray'
    }
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900">Repayment Analysis</h1>
        <p className="text-secondary-600 mt-2">
          Track your repayment history and payment performance metrics
        </p>
      </div>

      {/* Timeframe Selector */}
      <div className="mb-6">
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          <option value="3m">Last 3 months</option>
          <option value="6m">Last 6 months</option>
          <option value="1y">Last year</option>
          <option value="all">All time</option>
        </select>
      </div>

      {/* Payment Score Overview */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Score</h3>
        <div className="flex items-center mb-4">
          <div className={`bg-${paymentLevel.color}-100 p-3 rounded-lg mr-4`}>
            <CheckCircle className={`h-8 w-8 text-${paymentLevel.color}-600`} />
          </div>
          <div>
            <p className="text-2xl font-bold text-secondary-900">{paymentLevel.level}</p>
            <p className="text-sm text-secondary-600">Score: {repaymentData.paymentScore}/100</p>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`bg-${paymentLevel.color}-600 h-2 rounded-full`}
            style={{ width: `${repaymentData.paymentScore}%` }}
          ></div>
        </div>
      </div>

      {/* Repayment Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-lg">
              <CreditCard className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Total Paid</p>
              <p className="text-2xl font-bold text-secondary-900">
                ${repaymentData.totalPaid.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg">
              <CheckCircle className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">On-Time Payments</p>
              <p className="text-2xl font-bold text-secondary-900">{repaymentData.onTimePayments}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-red-100 p-3 rounded-lg">
              <TrendingUp className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Late Payments</p>
              <p className="text-2xl font-bold text-secondary-900">{repaymentData.latePayments}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-lg">
              <Calendar className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-600">Next Payment</p>
              <p className="text-lg font-bold text-secondary-900">
                ${repaymentData.nextPaymentAmount.toLocaleString()}
              </p>
              <p className="text-xs text-secondary-500">{repaymentData.nextPaymentDue}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Payment History */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment History</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Month</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Amount</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Due Date</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
              </tr>
            </thead>
            <tbody>
              {repaymentData.monthlyPayments.map((payment, index) => (
                <tr key={index} className="border-b border-gray-100">
                  <td className="py-3 px-4 text-sm text-gray-900">{payment.month}</td>
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">
                    ${payment.amount.toLocaleString()}
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-900">{payment.dueDate}</td>
                  <td className="py-3 px-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-${getStatusColor(payment.status)}-100 text-${getStatusColor(payment.status)}-800`}>
                      {payment.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Payment Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Patterns</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm text-gray-600">Average Payment Amount</span>
              <span className="text-sm font-medium text-gray-900">$2,500</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm text-gray-600">Payment Frequency</span>
              <span className="text-sm font-medium text-gray-900">Monthly</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm text-gray-600">On-Time Rate</span>
              <span className="text-sm font-medium text-green-600">94.7%</span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="text-sm text-gray-600">Early Payment Bonus</span>
              <span className="text-sm font-medium text-blue-600">$125 saved</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recommendations</h3>
          <div className="space-y-3">
            <div className="p-3 bg-green-50 rounded-lg">
              <p className="text-sm font-medium text-green-900">Excellent Record</p>
              <p className="text-xs text-green-700">Your payment history qualifies you for premium rates on future loans</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm font-medium text-blue-900">Auto-Pay Setup</p>
              <p className="text-xs text-blue-700">Set up automatic payments to maintain your excellent score</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg">
              <p className="text-sm font-medium text-purple-900">Early Payment</p>
              <p className="text-xs text-purple-700">Consider early payments to reduce total interest costs</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RepaymentAnalysisPage
