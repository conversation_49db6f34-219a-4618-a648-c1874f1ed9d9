import React from 'react'
import { Users, DollarSign, FileText, TrendingUp } from 'lucide-react'
import { useLoans, useUsers, useInvestments } from '../../hooks/useApi'

const AdminDashboard = () => {
  // Fetch real data from Supabase
  const { data: loans, isLoading: loansLoading } = useLoans()
  const { data: users, isLoading: usersLoading } = useUsers()
  const { data: investments, isLoading: investmentsLoading } = useInvestments()

  // Calculate stats from real data
  const stats = {
    totalLoans: loans?.length || 0,
    totalInvestors: users?.filter(user => user.role === 'investor').length || 0,
    totalFunded: investments?.reduce((sum, inv) => sum + (inv.amount || 0), 0) || 0,
    activeLoans: loans?.filter(loan => loan.status === 'active').length || 0,
  }

  // Get recent loans (last 5)
  const recentLoans = loans?.slice(0, 5) || []

  const isLoading = loansLoading || usersLoading || investmentsLoading

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-10">
        <h1 className="text-4xl font-bold text-navy-900 mb-3">Admin Dashboard</h1>
        <p className="text-secondary-600 text-lg">
          Platform overview and key metrics
        </p>
      </div>

      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft border border-cream-200/50 p-8 transition-all duration-200 hover:shadow-medium group">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-secondary-600 uppercase tracking-wide mb-2">Total Loans</p>
                <p className="text-3xl font-bold text-navy-900">{stats.totalLoans}</p>
              </div>
              <div className="bg-gradient-to-br from-primary-500 to-primary-600 p-3 rounded-xl shadow-soft group-hover:shadow-medium transition-all duration-200">
                <FileText className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft border border-cream-200/50 p-8 transition-all duration-200 hover:shadow-medium group">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-secondary-600 uppercase tracking-wide mb-2">Total Investors</p>
                <p className="text-3xl font-bold text-navy-900">{stats.totalInvestors}</p>
              </div>
              <div className="bg-gradient-to-br from-success-500 to-success-600 p-3 rounded-xl shadow-soft group-hover:shadow-medium transition-all duration-200">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft border border-cream-200/50 p-8 transition-all duration-200 hover:shadow-medium group">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-secondary-600 uppercase tracking-wide mb-2">Total Funded</p>
                <p className="text-3xl font-bold text-navy-900">
                  ${stats.totalFunded.toLocaleString()}
                </p>
              </div>
              <div className="bg-gradient-to-br from-warning-500 to-warning-600 p-3 rounded-xl shadow-soft group-hover:shadow-medium transition-all duration-200">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft border border-cream-200/50 p-8 transition-all duration-200 hover:shadow-medium group">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-secondary-600 uppercase tracking-wide mb-2">Active Loans</p>
                <p className="text-3xl font-bold text-navy-900">{stats.activeLoans}</p>
              </div>
              <div className="bg-gradient-to-br from-cream-500 to-cream-600 p-3 rounded-xl shadow-soft group-hover:shadow-medium transition-all duration-200">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Recent Loans Table */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft border border-cream-200/50 p-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-navy-900">Recent Loan Applications</h2>
            <button className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-xl hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 font-semibold">
              View All
            </button>
          </div>

          <div className="overflow-x-auto rounded-xl border border-cream-200/50">
            <table className="min-w-full divide-y divide-cream-200/50">
              <thead className="bg-cream-50/50">
                <tr>
                  <th className="px-8 py-4 text-left text-xs font-bold text-secondary-600 uppercase tracking-wider">
                    Borrower
                  </th>
                  <th className="px-8 py-4 text-left text-xs font-bold text-secondary-600 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-8 py-4 text-left text-xs font-bold text-secondary-600 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-8 py-4 text-left text-xs font-bold text-secondary-600 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-8 py-4 text-left text-xs font-bold text-secondary-600 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white/50 divide-y divide-cream-200/30">
                {recentLoans.length === 0 ? (
                  <tr>
                    <td colSpan="5" className="px-8 py-12 text-center text-sm text-secondary-500">
                      No recent loan applications
                    </td>
                  </tr>
                ) : (
                  recentLoans.map((loan) => (
                    <tr key={loan.id} className="hover:bg-cream-50/50 transition-colors duration-150">
                      <td className="px-8 py-5 whitespace-nowrap text-sm font-semibold text-navy-900">
                        {loan.borrower_name || 'N/A'}
                      </td>
                      <td className="px-8 py-5 whitespace-nowrap text-sm font-medium text-secondary-700">
                        ${(loan.amount || 0).toLocaleString()}
                      </td>
                      <td className="px-8 py-5 whitespace-nowrap">
                        <span className={`inline-flex px-3 py-1.5 text-xs font-bold rounded-lg ${
                          loan.status === 'pending' ? 'bg-warning-100 text-warning-800' :
                          loan.status === 'funded' ? 'bg-success-100 text-success-800' :
                          loan.status === 'approved' ? 'bg-primary-100 text-primary-800' :
                          'bg-secondary-100 text-secondary-800'
                        }`}>
                          {loan.status || 'unknown'}
                        </span>
                      </td>
                      <td className="px-8 py-5 whitespace-nowrap text-sm text-secondary-600">
                        {loan.created_at ? new Date(loan.created_at).toLocaleDateString() : 'N/A'}
                      </td>
                      <td className="px-8 py-5 whitespace-nowrap text-sm font-medium space-x-3">
                        <button className="text-primary-600 hover:text-primary-700 font-semibold transition-colors">
                          Review
                        </button>
                        <button className="text-secondary-600 hover:text-secondary-700 font-semibold transition-colors">
                          Details
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
